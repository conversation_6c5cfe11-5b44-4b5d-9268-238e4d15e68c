<PERSON><PERSON> thủ hướng dẫn trong @.github/copilot-instructions.md

# <PERSON><PERSON>c huớng dẫn bổ sung

- Clean Architecture & DDD trong Java Spring @.github/instructions/clean-architecture.instructions.md
- Containerization & Docker Best Practices @.github/instructions/containerization-docker-best-practices.instructions.md
- GitHub Actions CI/CD Best Practices @.github/instructions/github-actions-ci-cd-best-practices.instructions.md
- Java Development @.github/instructions/java.instructions.md
- Kubernetes Deployment Best Practices @.github/instructions/kubernetes-deployment-best-practices.instructions.md
- Quy tắc nội dung Markdown @.github/instructions/markdown.instructions.md
- Hướng dẫn phát triển Next.js + Tailwind @.github/instructions/nextjs-tailwind.instructions.md
- Hướng dẫn phát triển ReactJS @.github/instructions/reactjs.instructions.md
- Phát triển Spring Boot @.github/instructions/springboot.instructions.md
- Hướng dẫn Phát triển TanStack Start với Shadcn/ui @.github/instructions/tanstack-start-shadcn-tailwind.instructions.md
- TypeScript Style Guide @.github/instructions/typescript.google.instructions.md