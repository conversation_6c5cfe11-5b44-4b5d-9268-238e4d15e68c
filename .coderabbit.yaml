# yaml-language-server: $schema=https://storage.googleapis.com/coderabbit_public_assets/schema.v2.json

language: vi-VN
tone_instructions: "Phản hồi phải ngắn gọn, đ<PERSON><PERSON> g<PERSON>, d<PERSON> hiểu. <PERSON><PERSON><PERSON> nguyên thuật ngữ tiếng Anh chuyên ngành trong lĩnh vực Công nghệ thông tin và Phát triển phần mềm"
reviews:
  high_level_summary_placeholder: "@coderabbitai summary"
  auto_title_placeholder: "@coderabbitai title"
  request_changes_workflow: true
  auto_title_instructions: "[Loại Thay <PERSON>ổi]: [<PERSON><PERSON> Tả Ngắn Gọn Về Thay Đổi]"
  auto_apply_labels: true
  labeling_instructions:
    - label: "feature"
      instructions: "Khi PR thêm một tính năng mới"
    - label: "bugfix"
      instructions: "Khi PR sửa một lỗi"
    - label: "documentation"
      instructions: "<PERSON><PERSON> <PERSON> cập nhật tài liệu (README, hướng dẫn sử dụng, comments trong code)"
    - label: "refactor"
      instructions: "<PERSON><PERSON> PR chỉ cải thiện cấu trúc, hi<PERSON><PERSON> suất hoặc tính dễ đọc của code mà không thay đổi chức năng"
    - label: "style"
      instructions: "Khi PR chỉ thay đổi định dạng code (whitespace, linting fixes)"
    - label: "test"
      instructions: "Khi PR thêm hoặc sửa các bài kiểm tra (tests)"
    - label: "chore"
      instructions: "Các công việc bảo trì nhỏ, cập nhật phụ thuộc không ảnh hưởng đến chức năng chính"
    - label: "hotfix"
      instructions: "Sửa lỗi khẩn cấp cần được triển khai nhanh chóng"
    - label: "performance"
      instructions: "Khi PR tối ưu hóa hiệu suất"
  auto_assign_reviewers: true
  estimate_code_review_effort: true
  sequence_diagrams: true
  poem: false
  path_filters:
    - "**/*.{ts,tsx}"
    - "**/*.{css,scss}"
    - "**/*.json"
    - "**/*.java"
    - "**/Dockerfile"
    - "**/*.dockerfile"
    - "**/.github/workflows/*.{yml,yaml}"
    - "**/*.md"
    - "!**/dist/**"
    - "!**/node_modules/**"
  auto_review:
    enabled: true
    base_branches:
      - "develop"
      - "main"
knowledge_base:
  code_guidelines:
    enabled: true
    filePatterns:
      - "**/docs/*.md"
      - "**/.github/copilot-instructions.md"
      - "**/.github/instructions/*.instructions.md"
code_generation:
  docstrings:
    language: vi-VN
