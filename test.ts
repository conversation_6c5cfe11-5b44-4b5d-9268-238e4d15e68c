import * as _FooBar from './foo_bar.ts';
import IUserService from './user-service';

let my_private_data;

class MyClass {
    #internalState = 10;
    public static MY_CONSTANT = 'some_value';

    constructor(private _name_: string, public age: number) {
    }

    get SomeValue() {
        console.log("Accessing some value!");
        return this.#internalState;
    }

    doSomething = () => {
        if (this.#internalState > 5)
            throw new Error('Something went wrong')

        for (let i in [1, 2, 3]) {
            console.log(i)
        }

        const value = '123' as unknown as Number
        console.log(new String('test'))
    }

    private anotherMethod(param: any) {
        debugger;
        const arr = new Array(3);
        arr.forEach(item => console.log(item));
    }
}

function processData(data: Object) {
    if (data == null) {
        console.log("Data is null or undefined");
    }
}

export default class MyUtil {
    static helper() {
        return 'This is a helper';
    }
}