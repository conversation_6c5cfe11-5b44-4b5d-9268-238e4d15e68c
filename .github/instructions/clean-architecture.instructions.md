---
description: 'Hướng dẫn xây dựng ứng dụng Clean Architecture & DDD'
applyTo: '**/*.java'
---

# **Clean Architecture & DDD trong Java Spring**

## **1\. Triết lý cốt lõi & <PERSON><PERSON>c tiêu**

**Mục tiêu chính:** Xây dựng một ứng dụng Spring Boot mạnh mẽ, có thể mở rộng và dễ bảo trì bằng cách tuân thủ nghiêm ngặt các nguyên tắc **Clean Architecture** và **Domain-Driven Design (DDD)**.

* **Clean Architecture:** Nguyên tắc cốt lõi là **Dependency Rule** (Quy tắc phụ thuộc). Các phụ thuộc source code chỉ có thể hướng vào trong. Không có gì ở vòng tròn bên trong được phép biết bất cứ điều gì về vòng tròn bên ngoài. Điều này có nghĩa là các layer Domain và Application phải độc lập với các chi tiết Infrastructure như database, web framework, hoặc UI.
* **Domain-Driven Design (DDD):** Tập trung vào core business domain. Chúng ta mô hình hóa business logic bằng cách sử dụng các building block của DDD (Aggregate, Entity, Value Object, Domain Service). Code phải phản ánh trực tiếp business domain.

## **2\. Các layer kiến trúc & Cấu trúc package**

Dự án được cấu trúc thành ba layer chính, được biểu diễn bằng các parent package. **Tất cả code mới phải được đặt đúng layer.**

```
com.yourcompany.yourproject  
├── domain                  // Layer 1: Core (Không có Spring dependency ở đây!)  
│   ├── model  
│   │   ├── order           // Ví dụ Aggregate  
│   │   │   ├── Order.java  // Aggregate Root (Entity)  
│   │   │   ├── OrderItem.java // Entity trong Aggregate  
│   │   │   ├── OrderStatus.java // Enum (Value Object)  
│   │   │   └── CustomerId.java // Value Object  
│   │   └── ...  
│   ├── service  
│   │   └── PricingService.java // Domain Service  
│   └── repository  
│       └── OrderRepository.java // Repository Interface (contract)  
│  
├── application             // Layer 2: Use Case  
│   ├── dto                 // Data Transfer Object  
│   │   ├── CreateOrderRequest.java  
│   │   └── OrderResponse.java  
│   ├── service             // Application Service (Use Case)  
│   │   └── OrderApplicationService.java  
│   └── exception  
│       └── OrderNotFoundException.java  
│  
└── infrastructure          // Layer 3: Framework & Driver  
    ├── configuration  
    │   └── BeanConfiguration.java  
    ├── persistence         // JPA Implementation của Repository  
    │   ├── entity          // JPA @Entity class (Data Mapper)  
    │   │   └── OrderJpaEntity.java  
    │   ├── repository      // Implementation của domain repository interface  
    │   │   └── OrderRepositoryImpl.java  
    │   └── mapper  
    │       └── OrderMapper.java // Map giữa Domain model và JPA entity  
    └── web  
        ├── controller  
        │   └── OrderController.java // Spring REST Controller  
        └── mapper  
            └── OrderApiMapper.java // Map giữa DTO và Domain model
```

## **3\. Nguyên tắc & Quy tắc chính**

### **Domain Layer**

* **Thuần túy & Độc lập:** Không chứa **BẤT KỲ** Spring annotation nào (@Service, @Component) và **KHÔNG** có dependency vào bất kỳ layer hoặc framework nào khác (ngoại trừ thư viện Java chuẩn hoặc thư viện DDD chuyên dụng).
* **DDD Building Block:**  
  * **Aggregate:** Một nhóm domain object (Entity, Value Object) được xem như một đơn vị duy nhất. **Aggregate Root** là điểm truy cập duy nhất vào aggregate. Tất cả tham chiếu bên ngoài phải đi qua Aggregate Root.
  * **Entity:** Một object có danh tính riêng biệt tồn tại qua thời gian và các trạng thái khác nhau (ví dụ: Order, Customer). Sử dụng Long hoặc UUID cho ID.
  * **Value Object:** Một object bất biến được định nghĩa bởi các thuộc tính, không phải danh tính (ví dụ: Money, Address). Sử dụng Java record khi có thể để đảm bảo tính bất biến.
  * **Repository Interface:** Định nghĩa contract cho persistence (ví dụ: save(Order order), findById(OrderId id)). *Interface* nằm ở đây, nhưng *implementation* ở Infrastructure layer.
  * **Domain Service:** Chứa domain logic không phù hợp để đặt trong Entity hoặc Value Object.

### **Application Layer**

* **Điều phối Use Case:** Layer này chứa các business rule cụ thể của ứng dụng. Nó điều phối các domain object để thực hiện task.
* **Application Service:**  
  * Các public method trên service này đại diện cho use case của ứng dụng (ví dụ: createOrder, addProductToCart).
  * Chúng là client chính của Domain layer.
  * Có thể sử dụng Spring annotation như @Service và @Transactional.
* **DTO (Data Transfer Object):** Sử dụng DTO cho input (request) và output (response) của application service. **Không bao giờ** expose domain entity trực tiếp ra bên ngoài (ví dụ: web layer).
* **Dependency:** Chỉ phụ thuộc vào Domain layer. Nó định nghĩa exception riêng (ví dụ: OrderNotFoundException).

### **Infrastructure Layer**

* **Chi tiết "bẩn":** Đây là nơi tất cả các concern bên ngoài tồn tại: web controller, database implementation, message broker, v.v.
* **Implementation của Interface:** Layer này implement các repository interface được định nghĩa trong Domain layer. Đây chính là **Dependency Inversion** trong thực tế.
  * OrderRepositoryImpl.java sẽ implement domain.repository.OrderRepository và sử dụng Spring Data JPA (JpaRepository) bên trong.
* **Mapper:** Chứa các class chịu trách nhiệm mapping data giữa các layer:
  * Domain Model <-> JPA Entity (Order <-> OrderJpaEntity)
  * Domain Model <-> DTO (Order <-> OrderResponse)
* **Spring Controller:** Spring @RestController chuẩn. Chúng nên mỏng và chỉ delegate call đến Application layer. **Không có business logic ở đây.**
* **Dependency:** Layer này phụ thuộc vào Application và Domain layer để gọi service và sử dụng model.

## **4\. Ví dụ task: "Tạo Order mới"**

**Copilot, hãy implement use case tạo order mới.**

1. **Application Layer (DTO):**  
   * Tạo CreateOrderRequest.java DTO trong application.dto với các field như customerId và danh sách productItem.
2. **Application Layer (Service):**  
   * Trong OrderApplicationService.java, tạo method public OrderResponse createOrder(CreateOrderRequest request).
   * Method này sẽ:  
     a. Sử dụng mapper để convert CreateOrderRequest DTO thành domain object.
     b. Tạo instance Order aggregate root mới bằng static factory method như Order.create(...).
     c. Gọi orderRepository.save(newOrder) để persist nó.
     d. Map Order domain object kết quả thành OrderResponse DTO và return.
3. **Infrastructure Layer (Controller):**  
   * Trong OrderController.java, tạo @PostMapping endpoint.
   * Nó nhận @RequestBody CreateOrderRequest.
   * Nó gọi orderApplicationService.createOrder(request).
   * Nó return ResponseEntity<OrderResponse>.
4. **Infrastructure Layer (Persistence):**  
   * Đảm bảo OrderRepositoryImpl.java implement đúng save method.
   * Nó sẽ map domain Order thành OrderJpaEntity và sử dụng Spring Data JpaRepository để save vào database.

## **5\. Những điều cần TRÁNH ⛔**

* **Leaking Abstraction:** **KHÔNG BAO GIỜ** return JPA entity (@Entity) từ repository. Luôn return domain model object.
* **Business Logic trong Controller:** Controller chỉ dành cho HTTP và routing.
* **Fat Service:** Giữ Application Service tập trung vào một use case duy nhất. Tránh tạo một service class quá lớn.
* **Vi phạm Dependency Rule:** Một import statement trong domain package trỏ đến infrastructure hoặc application là lỗi nghiêm trọng.