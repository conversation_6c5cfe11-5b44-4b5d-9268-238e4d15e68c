---
description: 'Tiêu chuẩn tạo tài liệu và nội dung'
applyTo: '**/*.md'
---

## Quy tắc nội dung Markdown

Các quy tắc nội dung markdown sau được áp dụng trong validators:

1. **Headings**: S<PERSON> dụng các cấp độ heading phù hợp (H2, H3, v.v.) để cấu trúc nội dung của bạn. Không sử dụng heading H1, vì điều này sẽ được tạo dựa trên title.
2. **Lists**: Sử dụng bullet points hoặc numbered lists cho danh sách. Đảm bảo indentation và spacing phù hợp.
3. **Code Blocks**: Sử dụng fenced code blocks cho code snippets. Chỉ định ngôn ngữ để syntax highlighting.
4. **Links**: Sử dụng cú pháp markdown đúng cho links. Đ<PERSON><PERSON> bảo rằng links hợp lệ và có thể truy cập được.
5. **Images**: Sử dụng cú pháp markdown đúng cho images. <PERSON>o gồm alt text để accessibility.
6. **Tables**: Sử dụng markdown tables cho dữ liệu dạng bảng. Đảm bảo formatting và alignment đúng.
7. **Line Length**: Giới hạn độ dài dòng tối đa 400 ký tự để dễ đọc.
8. **Whitespace**: Sử dụng whitespace phù hợp để phân tách các phần và cải thiện khả năng đọc.
9. **Front Matter**: Bao gồm YAML front matter ở đầu file với các trường metadata bắt buộc.

## Formatting và cấu trúc

Tuân theo các hướng dẫn sau để formatting và cấu trúc nội dung markdown của bạn:

- **Headings**: Sử dụng `##` cho H2 và `###` cho H3. Đảm bảo rằng headings được sử dụng theo thứ bậc. Khuyến nghị cấu trúc lại nếu nội dung bao gồm H4, và khuyến nghị mạnh hơn cho H5.
- **Lists**: Sử dụng `-` cho bullet points và `1.` cho numbered lists. Indent các nested lists bằng hai dấu cách.
- **Code Blocks**: Sử dụng triple backticks (` ``` `) để tạo fenced code blocks. Chỉ định ngôn ngữ sau opening backticks để syntax highlighting (ví dụ: ` ```csharp `).
- **Links**: Sử dụng `[link text](URL)` cho links. Đảm bảo rằng link text mô tả rõ ràng và URL hợp lệ.
- **Images**: Sử dụng `![alt text](image URL)` cho images. Bao gồm mô tả ngắn gọn về image trong alt text.
- **Tables**: Sử dụng `|` để tạo tables. Đảm bảo rằng các cột được căn chỉnh đúng và có headers.
- **Line Length**: Ngắt dòng tại 80 ký tự để cải thiện khả năng đọc. Sử dụng soft line breaks cho các đoạn văn dài.
- **Whitespace**: Sử dụng blank lines để phân tách các phần và cải thiện khả năng đọc. Tránh whitespace quá mức.

## Yêu cầu validation

Đảm bảo tuân thủ các yêu cầu validation sau:

- **Front Matter**: Bao gồm các trường sau trong YAML front matter:

  - `post_title`: Title của post.
  - `author1`: Tác giả chính của post.
  - `post_slug`: URL slug cho post.
  - `microsoft_alias`: Microsoft alias của tác giả.
  - `featured_image`: URL của featured image.
  - `categories`: Các categories cho post. Những categories này phải nằm trong danh sách ở /categories.txt.
  - `tags`: Các tags cho post.
  - `ai_note`: Ghi chú nếu AI được sử dụng trong việc tạo post.
  - `summary`: Tóm tắt ngắn gọn của post. Khuyến nghị summary dựa trên nội dung khi có thể.
  - `post_date`: Ngày xuất bản của post.

- **Content Rules**: Đảm bảo rằng nội dung tuân theo các quy tắc nội dung markdown được chỉ định ở trên.
- **Formatting**: Đảm bảo rằng nội dung được formatted và cấu trúc đúng theo các hướng dẫn.
- **Validation**: Chạy các validation tools để kiểm tra sự tuân thủ các quy tắc và hướng dẫn.
