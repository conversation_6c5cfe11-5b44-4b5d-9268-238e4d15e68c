---
description: 'Hướng dẫn xây dựng ứng dụng cơ bản với Spring Boot'
applyTo: '**/*.java'
---

# Phát triển Spring Boot

## Hướng dẫn chung

- Chỉ đưa ra những gợi ý có độ tin cậy cao khi review các thay đổi code.
- Viết code với các thực hành maintainability tốt, bao gồm comment giải thích lý do cho các quyết định thiết kế.
- Xử lý các edge case và viết exception handling rõ ràng.
- Đối với library hoặc external dependency, đề cập đến mục đích sử dụng trong comment.

## Hướng dẫn Spring Boot

### Dependency Injection

- Sử dụng constructor injection cho tất cả dependency bắt buộc.
- Khai báo các field dependency là `private final`.

### Configuration

- Sử dụng file YAML (`application.yml`) cho externalized configuration.
- Environment Profiles: Sử dụng Spring profile cho các môi trường <PERSON> (dev, test, prod)
- Configuration Properties: Sử dụng @ConfigurationProperties cho type-safe configuration binding
- Secrets Management: Externalize secret bằng environment variable hoặc secret management system

### Code Organization

- Package Structure: Tổ chức theo feature/domain thay vì theo layer
- Separation of Concerns: Giữ controller gọn gàng, service tập trung, và repository đơn giản
- Utility Classes: Tạo utility class final với private constructor

### Service Layer

- Đặt business logic trong class có annotation `@Service`.
- Service nên stateless và testable.
- Inject repository thông qua constructor.
- Method signature của service nên sử dụng domain ID hoặc DTO, không expose repository entity trực tiếp trừ khi cần thiết.

### Logging

- Sử dụng SLF4J cho tất cả logging (`private static final Logger logger = LoggerFactory.getLogger(MyClass.class);`).
- Không sử dụng concrete implementation (Logback, Log4j2) hoặc `System.out.println()` trực tiếp.
- Sử dụng parameterized logging: `logger.info("User {} logged in", userId);`.

### Security & Input Handling

- Sử dụng parameterized query | Luôn sử dụng Spring Data JPA hoặc `NamedParameterJdbcTemplate` để ngăn chặn SQL injection.
- Validate request body và parameter bằng JSR-380 annotation (`@NotNull`, `@Size`, etc.) và `BindingResult`

## Build và Verification

- Sau khi thêm hoặc chỉnh sửa code, verify rằng project vẫn build thành công.
- Nếu project sử dụng Maven, chạy `mvn clean install`.
- Nếu project sử dụng Gradle, chạy `./gradlew build` (hoặc `gradlew.bat build` trên Windows).
- Đảm bảo tất cả test pass trong quá trình build.
