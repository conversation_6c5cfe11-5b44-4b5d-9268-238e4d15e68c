---
description: 'Tiêu chuẩn và hướng dẫn phát triển Next.js + Tailwind'
applyTo: '**/*.tsx, **/*.ts, **/*.jsx, **/*.js, **/*.css'
---

# Hướng dẫn phát triển Next.js + Tailwind

Hướng dẫn cho các ứng dụng Next.js chất lượng cao với Tailwind CSS styling và TypeScript.

## Bối cảnh dự án

- Next.js phiên bản mới nhất (App Router)
- TypeScript cho type safety
- Tailwind CSS cho styling

## Tiêu chuẩn phát triển

### Architecture
- App Router với server và client components
- Nhóm routes theo feature/domain
- Implement error boundaries phù hợp
- Sử dụng React Server Components theo mặc định
- Tận dụng static optimization khi có thể

### TypeScript
- Bật strict mode
- Định nghĩa type rõ ràng
- <PERSON><PERSON> lý lỗi đúng cách với type guards
- Zod cho runtime type validation

### Styling
- Tailwind CSS với color palette nhất quán
- Responsive design patterns
- Hỗ trợ dark mode
- Tuân theo container queries best practices
- Duy trì cấu trúc semantic HTML

### State Management
- React Server Components cho server state
- React hooks cho client state
- Loading và error states phù hợp
- Optimistic updates khi thích hợp

### Data Fetching
- Server Components cho direct database queries
- React Suspense cho loading states
- Xử lý lỗi và retry logic phù hợp
- Cache invalidation strategies

### Security
- Input validation và sanitization
- Kiểm tra authentication phù hợp
- CSRF protection
- Rate limiting implementation
- Xử lý API route bảo mật

### Performance
- Image optimization với next/image
- Font optimization với next/font
- Route prefetching
- Code splitting phù hợp
- Bundle size optimization

## Quy trình implementation
1. Lập kế hoạch component hierarchy
2. Định nghĩa types và interfaces
3. Implement server-side logic
4. Xây dựng client components
5. Thêm error handling phù hợp
6. Implement responsive styling
7. Thêm loading states
8. Viết tests
