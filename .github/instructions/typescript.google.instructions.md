---
description: "Quy tắc và hướng dẫn coding TypeScript tại Google"
applyTo: "**/*.ts"
---

# TypeScript Style Guide - Quy tắc Cốt lõi

## **1. Identifier & Naming**

- **<PERSON>ý tự Cho phép:** Chỉ sử dụng ASCII letter, digit, underscore (`_`), và dollar sign (`$`). Regex: `[$\w]+`.
- **Quy ước Case:**
  - `UpperCamelCase`: class, interface, type, enum, decorator, type parameter.
  - `lowerCamelCase`: variable, parameter, function, method, property, module alias.
  - `CONSTANT_CASE`: Global constant, enum value, `static readonly` property được dùng như constant.
- **Private Identifier:** **KHÔNG** sử dụng `#ident` cho private member. Sử dụng keyword `private`.
- **Underscore:** **KHÔNG** sử dụng leading hoặc trailing underscore (ví dụ: `_name_`).
- **Dollar Sign ($):** Tránh `$` trừ khi được yêu cầu bởi third-party framework (ví dụ: Observable).
- **Import:** Module namespace import sử dụng `lowerCamelCase`. Filename sử dụng `snake_case` (ví dụ: `import * as fooBar from "./foo_bar";`).
- **React Component:** Phải là `UpperCamelCase`.
- **Interface:** **KHÔNG** prefix với `I` (ví dụ: `IMyInterface`).
- **Alias:** Phải khớp với naming format của symbol gốc. Sử dụng `const` cho variable alias và `readonly` cho class field alias.

## **2. Formatting & Comment**

- **File Encoding:** Sử dụng UTF-8.
- **Formatting:** Sử dụng automated formatter.
- **Comment:**
  - `/** JSDoc */`: Cho documentation dành cho user của code (API, export).
  - `// line comment`: Cho implementation detail.
- **JSDoc:**
  - Đặt trước decorator.
  - Bỏ qua type annotation dư thừa (`@param {string}`, `@implements`) đã được TypeScript syntax cover.
  - **KHÔNG** sử dụng `@override`.
  - Cho parameter property (`constructor(private readonly name: string)`), document với `@param`.
- **Semicolon:** Luôn kết thúc statement với semicolon. **KHÔNG** dựa vào Automatic Semicolon Insertion (ASI).

## **3. Language Rule**

### **Class & Member**

- **Visibility:** Giới hạn visibility càng nhiều càng tốt. **KHÔNG BAO GIỜ** sử dụng modifier `public` trừ khi declare non-readonly parameter property.
- **Constructor:**
  - Luôn sử dụng parentheses: `new Foo()`, không phải `new Foo`.
  - Bỏ qua empty constructor hoặc constructor chỉ gọi `super()` với cùng argument.
- **`readonly`:** Sử dụng `readonly` cho property không bao giờ được reassign ngoài constructor.
- **Parameter Property:** Sử dụng parameter property để đơn giản hóa initialization: `constructor(private readonly bar: BarService) {}`.
- **Field Initializer:** Initialize property tại declaration site khi có thể.
- **Getter/Setter:**
  - Có thể sử dụng, nhưng getter phải là pure function.
  - Tránh simple pass-through accessor. Nếu không có logic, làm property public.
- **Private Field:** Sử dụng `private`, không phải `#privateIdentifier`.

### **Type & Variable**

- **Primitive:** **KHÔNG** sử dụng wrapper object (`new String('s')`, `new Boolean(b)`, `new Number(n)`). Sử dụng literal (`'s'`, `b`, `n`).
- **Array Constructor:** **KHÔNG** sử dụng `new Array()`. Sử dụng `[]` cho initialization hoặc `Array.from()` cho sized array.
- **Type Coercion:**
  - Sử dụng `String()`, `Boolean()`, `!!`, hoặc template literal cho coercion.
  - Sử dụng `Number()` để parse number và luôn check `NaN`, trừ khi impossible by context.
  - **KHÔNG** sử dụng unary plus (`+`) để coerce thành number.
  - **KHÔNG** sử dụng `parseInt` hoặc `parseFloat` trừ khi parse non-base-10 number, và validate input trước.
- **Variable Declaration:** Sử dụng `const` by default. Sử dụng `let` chỉ cho variable sẽ được reassign. **KHÔNG BAO GIỜ** sử dụng `var`.

### **Control Flow & Operator**

- **Block:** Luôn sử dụng curly brace `{}` cho multi-line control flow statement (`if`, `for`, `while`).
- **Switch Statement:**
  - Phải include case `default`.
  - Non-empty case không được fall through (sử dụng `break`, `return`, hoặc `throw`).
- **Equality:** Luôn sử dụng `===` và `!==`. Exception: sử dụng `== null` để check cả `null` và `undefined`.

### **Function**

- **Declaration:** Sử dụng `function foo() {}` cho named function.
- **Expression:** Sử dụng arrow function (`=>`) thay vì keyword `function` cho expression/callback.
- **`this`:** Tránh sử dụng `this` trong free function. Sử dụng arrow function để preserve `this` từ lexical scope. **KHÔNG** rebind `this`.
- **Arrow Function as Property:** Thường tránh arrow function property trên class. Ưu tiên bind method tại call site với arrow function: `() => { this.myMethod(); }`.
  - **Exception:** Arrow function property phù hợp cho event handler cần uninstall, vì chúng provide stable reference.

### **Khác**

- **Exception:** Sử dụng `throw new Error(...)`.
- **Iteration:**
  - Sử dụng `for...of` để iterate array và iterable khác.
  - **KHÔNG** sử dụng `for...in` trên array.
  - **KHÔNG** sử dụng `for...in` trên object mà không có `hasOwnProperty` check.
  - **KHÔNG** sử dụng `Array.prototype.forEach`, `Set.prototype.forEach`, `Map.prototype.forEach`.
- **Spread Operator (`...`):**
  - Khi tạo object, chỉ spread object.
  - Khi tạo array, chỉ spread iterable.
  - **KHÔNG BAO GIỜ** spread `null` hoặc `undefined`.
- **`@ts-ignore`:** **KHÔNG** sử dụng. Fix underlying type issue.
- **Debugger:** **KHÔNG** include `debugger;` statement trong production code.
- **Decorator:** Chỉ sử dụng decorator do framework cung cấp (ví dụ: Angular, Polymer). Không define mới. Đặt JSDoc trước decorator.

## **4. Module & Source Organization**

- **Module:** Sử dụng ES6 module (`import`/`export`). **KHÔNG** sử dụng `namespace` hoặc `require`.
- **Export:**
  - Sử dụng named export: `export class Foo { ... }`.
  - **KHÔNG** sử dụng default export: `export default class Foo { ... }`.
  - Không export mutable binding (`export let`). Export getter thay thế.
  - Chỉ export symbol dành cho sử dụng bên ngoài module.
  - **KHÔNG** tạo "container class" chỉ có static member cho namespacing. Export function và constant trực tiếp.
- **Import:**
  - Sử dụng `import { Foo } from './foo';` (destructuring) hoặc `import * as foo from './foo';` (module import) tùy trường hợp.
  - Sử dụng relative path (`./`, `../`) cho import trong cùng project.
  - **KHÔNG** sử dụng `import type` hoặc `export type`. Toolchain xử lý tự động.

## **5. Type System**

- **Type Inference:** Dựa vào inference cho simple type (`string`, `number`, `boolean`, `new` expression). Thêm explicit type cho complex expression để cải thiện readability.
- **`null` vs `undefined`:** Cả hai đều chấp nhận được; hãy consistent với surrounding API. Type alias không được include `|null` hoặc `|undefined`. Thêm chúng tại usage site.
- **Optional Property:** Sử dụng optional syntax (`prop?: T`) thay vì `prop: T | undefined`.
- **`interface` vs `type`:** Sử dụng `interface` để define object shape. Sử dụng `type` để name primitive, union, hoặc tuple.
- **Array Type:** Sử dụng `T[]` cho simple type (ví dụ: `string[]`). Sử dụng `Array<T>` cho complex type (ví dụ: `Array<string | number>`). Tương tự cho `readonly T[]` vs `ReadonlyArray<T>`.
- **Indexable Type:** Provide meaningful label cho key: `{[userName: string]: number}`. Cân nhắc sử dụng ES6 `Map`.
- **`any` Type:
  1.  **Tránh `any`**.
  2.  Trước tiên, thử provide specific type hơn (`interface`, `T`, etc.).
  3.  Nếu type thực sự unknown, sử dụng `unknown` và thực hiện type-safe narrowing.
  4.  Nếu `any` thực sự cần thiết (ví dụ: mock trong test), suppress lint warning với giải thích rõ ràng: `// tslint:disable-next-line:no-any ... reason ...`.
- **`unknown` Type:** Ưu tiên `unknown` hơn `any`. Bạn phải narrow type của `unknown` value trước khi operate trên nó.
- **Tuple Type:** Sử dụng tuple syntax `[string, number]` thay vì tạo `Pair`-like interface. Để rõ ràng, cân nhắc return object với named property: `{host: string, port: number}`.
- **Wrapper Type:** **KHÔNG BAO GIỜ** sử dụng `String`, `Boolean`, `Number`, hoặc `Object`. Sử dụng `string`, `boolean`, `number`, và `object` hoặc `{}` tương ứng.
- **Type Assertion (`as`):**
  - Tránh type assertion (`x as Foo`) và non-nullability assertion (`y!`). Ưu tiên runtime check (`if (x instanceof Foo)`).
  - Nếu assertion không thể tránh, thêm comment giải thích tại sao nó safe.
  - Luôn sử dụng `as Foo`, **KHÔNG BAO GIỜ** `<Foo>x`.
  - Cho object literal, sử dụng type annotation (`const foo: Foo = ...`) thay vì assertion (`... as Foo`).
