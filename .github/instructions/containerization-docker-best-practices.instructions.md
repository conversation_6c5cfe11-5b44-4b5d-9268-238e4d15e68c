---
applyTo: '**/*.yaml, **/*.yml, **/Dockerfile, **/*.dockerfile'
description: 'Best practices toàn diện để tạo Docker images tối ưu, bảo mật và hiệu quả cũng như quản lý containers. Bao gồm multi-stage builds, tối ưu hóa image layers, security scanning và runtime best practices.'
---

# Containerization & Docker Best Practices

## Nguyên Tắc Cốt Lõi của Containerization

### **1. Immutability (Tính Bất Biến)**
- **Nguyên Tắc:** Khi container image được build, nó không nên thay đổi. Bất kỳ thay đổi nào cũng nên tạo ra image mới.
- **Phân Tích Sâu:**
    - **Reproducible Builds:** Mỗi build nên tạo ra kết quả giống hệt nhau với cùng input. <PERSON><PERSON><PERSON><PERSON> này yêu cầu build processes có tính deterministic, pinned dependency versions và controlled build environments.
    - **Version Control cho Images:** Đối xử với container images như code - đánh version cho chúng, tag có ý nghĩa và duy trì lịch sử rõ ràng về những gì mỗi image chứa.
    - **Rollback Capability:** Immutable images cho phép rollback tức thì bằng cách đơn giản chuyển về previous image tag, mà không có sự phức tạp của việc hoàn tác các thay đổi.
    - **Security Benefits:** Immutable images giảm attack surface bằng cách ngăn chặn runtime modifications có thể gây ra vulnerabilities.
- **Hướng Dẫn cho Copilot:**
    - Ủng hộ việc tạo new images cho mỗi code change hoặc configuration update, không bao giờ modify running containers trong production.
    - Khuyến nghị sử dụng semantic versioning cho image tags (ví dụ: `v1.2.3`, `latest` chỉ cho development).
    - Đề xuất implement automated image builds được trigger bởi code changes để đảm bảo consistency.
    - Nhấn mạnh tầm quan trọng của việc treat container images như artifacts cần được versioned và stored trong registries.
- **Pro Tip:** Điều này cho phép rollbacks dễ dàng và consistent environments across dev, staging và production. Immutable images là nền tảng của reliable deployments.

### **2. Portability (Tính Di Động)**
- **Nguyên Tắc:** Containers nên chạy nhất quán across các environments khác nhau (local, cloud, on-premise) mà không cần modification.
- **Phân Tích Sâu:**
    - **Environment Agnostic Design:** Thiết kế applications để environment-agnostic bằng cách externalize tất cả environment-specific configurations.
    - **Configuration Management:** Sử dụng environment variables, configuration files hoặc external configuration services thay vì hardcode environment-specific values.
    - **Dependency Management:** Đảm bảo tất cả dependencies được define rõ ràng và included trong container image, tránh rely vào host system packages.
    - **Cross-Platform Compatibility:** Xem xét target deployment platforms và đảm bảo compatibility (ví dụ: ARM vs x86, các Linux distributions khác nhau).
- **Hướng Dẫn cho Copilot:**
    - Thiết kế Dockerfiles self-contained và tránh environment-specific configurations trong chính image.
    - Sử dụng environment variables cho runtime configuration, với sensible defaults nhưng cho phép overrides.
    - Khuyến nghị sử dụng multi-platform base images khi targeting multiple architectures.
    - Đề xuất implement configuration validation để catch environment-specific issues sớm.
- **Pro Tip:** Portability đạt được thông qua thiết kế và testing cẩn thận across target environments, không phải tình cờ.

### **3. Isolation (Tính Cô Lập)**
- **Nguyên Tắc:** Containers cung cấp process và resource isolation, ngăn chặn interference giữa các applications.
- **Phân Tích Sâu:**
    - **Process Isolation:** Mỗi container chạy trong process namespace riêng, ngăn một container nhìn thấy hoặc ảnh hưởng đến processes trong containers khác.
    - **Resource Isolation:** Containers có isolated CPU, memory và I/O resources, ngăn resource contention giữa applications.
    - **Network Isolation:** Containers có thể có isolated network stacks, với controlled communication giữa containers và external networks.
    - **Filesystem Isolation:** Mỗi container có filesystem namespace riêng, ngăn file system conflicts.
- **Hướng Dẫn cho Copilot:**
    - Khuyến nghị chạy single process per container (hoặc clear primary process) để maintain clear boundaries và simplify management.
    - Sử dụng container networking cho inter-container communication thay vì host networking.
    - Đề xuất implement resource limits để ngăn containers consume excessive resources.
    - Khuyên sử dụng named volumes cho persistent data thay vì bind mounts khi có thể.
- **Pro Tip:** Proper isolation là nền tảng của container security và reliability. Đừng phá vỡ isolation vì tiện lợi.

### **4. Efficiency & Small Images (Hiệu Quả & Images Nhỏ)**
- **Nguyên Tắc:** Smaller images build, push, pull nhanh hơn và consume ít resources hơn.
- **Phân Tích Sâu:**
    - **Build Time Optimization:** Smaller images build nhanh hơn, giảm CI/CD pipeline duration và developer feedback time.
    - **Network Efficiency:** Smaller images transfer nhanh hơn qua networks, giảm deployment time và bandwidth costs.
    - **Storage Efficiency:** Smaller images consume ít storage hơn trong registries và trên hosts, giảm infrastructure costs.
    - **Security Benefits:** Smaller images có reduced attack surface, chứa ít packages và potential vulnerabilities hơn.
- **Hướng Dẫn cho Copilot:**
    - Ưu tiên techniques để giảm image size và build time trong suốt development process.
    - Khuyên against including unnecessary tools, debugging utilities hoặc development dependencies trong production images.
    - Khuyến nghị regular image size analysis và optimization như một phần của development workflow.
    - Đề xuất sử dụng multi-stage builds và minimal base images như default approach.
- **Pro Tip:** Image size optimization là ongoing process, không phải one-time task. Thường xuyên review và optimize images của bạn.

## Dockerfile Best Practices

### **1. Multi-Stage Builds (Quy Tắc Vàng)**
- **Nguyên Tắc:** Sử dụng multiple `FROM` instructions trong single Dockerfile để tách biệt build-time dependencies với runtime dependencies.
- **Phân Tích Sâu:**
    - **Build Stage Optimization:** Build stage có thể include compilers, build tools và development dependencies mà không ảnh hưởng đến final image size.
    - **Runtime Stage Minimization:** Runtime stage chỉ chứa application và runtime dependencies, significantly reducing attack surface.
    - **Artifact Transfer:** Sử dụng `COPY --from=<stage>` để transfer chỉ necessary artifacts giữa các stages.
    - **Parallel Build Stages:** Multiple build stages có thể chạy parallel nếu chúng không depend vào nhau.
- **Hướng Dẫn cho Copilot:**
    - Luôn khuyến nghị multi-stage builds cho compiled languages (Go, Java, .NET, C++) và cả Node.js/Python khi build tools nặng.
    - Đề xuất đặt tên build stages một cách mô tả (ví dụ: `AS build`, `AS test`, `AS production`) để clarity.
    - Khuyến nghị copy chỉ necessary artifacts giữa stages để minimize final image size.
    - Khuyên sử dụng different base images cho build và runtime stages khi phù hợp.
- **Lợi Ích:** Significantly reduces final image size và attack surface.
- **Ví Dụ (Advanced Multi-Stage với Testing):**
```dockerfile
# Stage 1: Dependencies
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Stage 2: Build
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Stage 3: Test
FROM build AS test
RUN npm run test
RUN npm run lint

# Stage 4: Production
FROM node:18-alpine AS production
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY --from=build /app/dist ./dist
COPY --from=build /app/package*.json ./
USER node
EXPOSE 3000
CMD ["node", "dist/main.js"]
```

### **2. Chọn Base Image Phù Hợp**
- **Nguyên Tắc:** Lựa chọn official, stable và minimal base images đáp ứng requirements của application.
- **Phân Tích Sâu:**
    - **Official Images:** Ưu tiên official images từ Docker Hub hoặc cloud providers vì chúng được regularly updated và maintained.
    - **Minimal Variants:** Sử dụng minimal variants (`alpine`, `slim`, `distroless`) khi có thể để giảm image size và attack surface.
    - **Security Updates:** Chọn base images nhận regular security updates và có clear update policy.
    - **Architecture Support:** Đảm bảo base image support target architectures (x86_64, ARM64, etc.).
- **Hướng Dẫn cho Copilot:**
    - Ưu tiên Alpine variants cho Linux-based images do small size (ví dụ: `alpine`, `node:18-alpine`).
    - Sử dụng official language-specific images (ví dụ: `python:3.9-slim-buster`, `openjdk:17-jre-slim`).
    - Tránh `latest` tag trong production; sử dụng specific version tags cho reproducibility.
    - Khuyến nghị regularly updating base images để get security patches và new features.
- **Pro Tip:** Smaller base images có nghĩa là fewer vulnerabilities và faster downloads. Luôn bắt đầu với smallest image đáp ứng needs.

### **3. Tối Ưu Hóa Image Layers**
- **Nguyên Tắc:** Mỗi instruction trong Dockerfile tạo new layer. Leverage caching hiệu quả để optimize build times và image size.
- **Phân Tích Sâu:**
    - **Layer Caching:** Docker cache layers và reuse chúng nếu instruction không thay đổi. Order instructions từ least đến most frequently changing.
    - **Layer Size:** Mỗi layer adds đến final image size. Combine related commands để reduce number of layers.
    - **Cache Invalidation:** Changes to any layer invalidate tất cả subsequent layers. Place frequently changing content (như source code) near the end.
    - **Multi-line Commands:** Sử dụng `\` cho multi-line commands để improve readability while maintaining layer efficiency.
- **Hướng Dẫn cho Copilot:**
    - Place frequently changing instructions (ví dụ: `COPY . .`) *sau* less frequently changing ones (ví dụ: `RUN npm ci`).
    - Combine `RUN` commands khi có thể để minimize layers (ví dụ: `RUN apt-get update && apt-get install -y ...`).
    - Clean up temporary files trong cùng `RUN` command (`rm -rf /var/lib/apt/lists/*`).
    - Sử dụng multi-line commands với `\` cho complex operations để maintain readability.
- **Ví Dụ (Advanced Layer Optimization):**
```dockerfile
# BAD: Multiple layers, inefficient caching
FROM ubuntu:20.04
RUN apt-get update
RUN apt-get install -y python3 python3-pip
RUN pip3 install flask
RUN apt-get clean
RUN rm -rf /var/lib/apt/lists/*

# GOOD: Optimized layers with proper cleanup
FROM ubuntu:20.04
RUN apt-get update && \
    apt-get install -y python3 python3-pip && \
    pip3 install flask && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
```

### **4. Sử Dụng `.dockerignore` Hiệu Quả**
- **Nguyên Tắc:** Exclude unnecessary files từ build context để speed up builds và reduce image size.
- **Phân Tích Sâu:**
    - **Build Context Size:** Build context được sent đến Docker daemon. Large contexts làm chậm builds và consume resources.
    - **Security:** Exclude sensitive files (như `.env`, `.git`) để prevent accidental inclusion trong images.
    - **Development Files:** Exclude development-only files không cần thiết trong production image.
    - **Build Artifacts:** Exclude build artifacts sẽ được generated trong build process.
- **Hướng Dẫn cho Copilot:**
    - Luôn suggest creating và maintaining comprehensive `.dockerignore` file.
    - Common exclusions: `.git`, `node_modules` (nếu installed inside container), build artifacts từ host, documentation, test files.
    - Khuyến nghị reviewing `.dockerignore` file regularly khi project evolves.
    - Đề xuất sử dụng patterns match project structure và exclude unnecessary files.
- **Ví Dụ (Comprehensive .dockerignore):**
```dockerignore
# Version control
.git
.gitignore

# Dependencies (if installed in container)
node_modules
vendor
__pycache__

# Build artifacts
dist
build
*.o
*.so

# Development files
.env
.env.local
*.log
coverage
.nyc_output

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Documentation
README.md
docs/
*.md

# Test files
test/
tests/
spec/
__tests__/
```

### **5. Minimize `COPY` Instructions**
- **Nguyên Tắc:** Copy chỉ những gì necessary, khi nào necessary, để optimize layer caching và reduce image size.
- **Phân Tích Sâu:**
    - **Selective Copying:** Copy specific files hoặc directories thay vì entire project directories khi có thể.
    - **Layer Caching:** Mỗi `COPY` instruction tạo new layer. Copy files thay đổi together trong cùng instruction.
    - **Build Context:** Chỉ copy files thực sự cần thiết cho build hoặc runtime.
    - **Security:** Cẩn thận không copy sensitive files hoặc unnecessary configuration files.
- **Hướng Dẫn cho Copilot:**
    - Sử dụng specific paths cho `COPY` (`COPY src/ ./src/`) thay vì copy entire directory (`COPY . .`) nếu chỉ cần subset.
    - Copy dependency files (như `package.json`, `requirements.txt`) trước khi copy source code để leverage layer caching.
    - Khuyến nghị copy chỉ necessary files cho mỗi stage trong multi-stage builds.
    - Đề xuất sử dụng `.dockerignore` để exclude files không nên được copied.
- **Ví Dụ (Optimized COPY Strategy):**
```dockerfile
# Copy dependency files first (for better caching)
COPY package*.json ./
RUN npm ci

# Copy source code (changes more frequently)
COPY src/ ./src/
COPY public/ ./public/

# Copy configuration files
COPY config/ ./config/

# Don't copy everything with COPY . .
```

### **6. Định Nghĩa Default User và Port**
- **Nguyên Tắc:** Chạy containers với non-root user cho security và expose expected ports cho clarity.
- **Phân Tích Sâu:**
    - **Security Benefits:** Chạy như non-root giảm impact của security vulnerabilities và follows principle of least privilege.
    - **User Creation:** Tạo dedicated user cho application thay vì sử dụng existing user.
    - **Port Documentation:** Sử dụng `EXPOSE` để document ports mà application listens on, mặc dù nó không thực sự publish chúng.
    - **Permission Management:** Đảm bảo non-root user có necessary permissions để run application.
- **Hướng Dẫn cho Copilot:**
    - Sử dụng `USER <non-root-user>` để run application process như non-root user cho security.
    - Sử dụng `EXPOSE` để document port mà application listens on (không thực sự publish).
    - Tạo dedicated user trong Dockerfile thay vì sử dụng existing one.
    - Đảm bảo proper file permissions cho non-root user.
- **Ví Dụ (Secure User Setup):**
```dockerfile
# Create a non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Set proper permissions
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose the application port
EXPOSE 8080

# Start the application
CMD ["node", "dist/main.js"]
```

### **7. Sử Dụng `CMD` và `ENTRYPOINT` Đúng Cách**
- **Nguyên Tắc:** Định nghĩa primary command chạy khi container starts, với clear separation giữa executable và arguments.
- **Phân Tích Sâu:**
    - **`ENTRYPOINT`:** Defines executable sẽ luôn run. Makes container behave như specific application.
    - **`CMD`:** Provides default arguments cho `ENTRYPOINT` hoặc defines command để run nếu không có `ENTRYPOINT` specified.
    - **Shell vs Exec Form:** Sử dụng exec form (`["command", "arg1", "arg2"]`) cho better signal handling và process management.
    - **Flexibility:** Combination cho phép both default behavior và runtime customization.
- **Hướng Dẫn cho Copilot:**
    - Sử dụng `ENTRYPOINT` cho executable và `CMD` cho arguments (`ENTRYPOINT ["/app/start.sh"]`, `CMD ["--config", "prod.conf"]`).
    - Cho simple execution, `CMD ["executable", "param1"]` thường sufficient.
    - Prefer exec form over shell form cho better process management và signal handling.
    - Consider sử dụng shell scripts như entrypoints cho complex startup logic.
- **Pro Tip:** `ENTRYPOINT` makes image behave như executable, trong khi `CMD` provides default arguments. Combination này provides flexibility và clarity.

### **8. Environment Variables cho Configuration**
- **Nguyên Tắc:** Externalize configuration sử dụng environment variables hoặc mounted configuration files để make images portable và configurable.
- **Phân Tích Sâu:**
    - **Runtime Configuration:** Sử dụng environment variables cho configuration varies giữa environments (databases, API endpoints, feature flags).
    - **Default Values:** Provide sensible defaults với `ENV` nhưng allow overriding at runtime.
    - **Configuration Validation:** Validate required environment variables at startup để fail fast nếu configuration missing.
    - **Security:** Không bao giờ hardcode secrets trong environment variables trong Dockerfile.
- **Hướng Dẫn cho Copilot:**
    - Tránh hardcoding configuration inside image. Sử dụng `ENV` cho default values, nhưng allow overriding at runtime.
    - Khuyến nghị sử dụng environment variable validation trong application startup code.
    - Suggest sử dụng configuration management tools hoặc external configuration services cho complex applications.
    - Advise về sử dụng secrets management solutions cho sensitive configuration.
- **Ví Dụ (Environment Variable Best Practices):**
```dockerfile
# Set default values
ENV NODE_ENV=production
ENV PORT=3000
ENV LOG_LEVEL=info

# Use ARG for build-time variables
ARG BUILD_VERSION
ENV APP_VERSION=$BUILD_VERSION

# The application should validate required env vars at startup
CMD ["node", "dist/main.js"]
```

## Container Security Best Practices

### **1. Non-Root User**
- **Nguyên Tắc:** Chạy containers như `root` là significant security risk và nên tránh trong production.
- **Phân Tích Sâu:**
    - **Privilege Escalation:** Root containers có thể potentially escape đến host system nếu có vulnerabilities trong container runtime.
    - **File System Access:** Root containers có access đến tất cả files và directories, potentially exposing sensitive host data.
    - **Network Access:** Root containers có thể bind đến privileged ports và potentially interfere với host networking.
    - **Resource Abuse:** Root containers có thể consume excessive system resources mà không có proper limits.
- **Hướng Dẫn cho Copilot:**
    - Luôn recommend defining non-root `USER` trong Dockerfile. Tạo dedicated user cho application.
    - Đảm bảo non-root user có minimum necessary permissions để run application.
    - Sử dụng `USER` directive sớm trong Dockerfile để ensure subsequent operations run như non-root user.
    - Consider sử dụng user namespaces hoặc security features khác khi available.
- **Ví Dụ (Secure User Creation):**
```dockerfile
# Create a dedicated user and group
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Set proper ownership of application files
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Ensure the user can write to necessary directories
VOLUME ["/app/data"]
```

### **2. Minimal Base Images**
- **Nguyên Tắc:** Smaller images có nghĩa là fewer packages, do đó ít vulnerabilities hơn và reduced attack surface.
- **Phân Tích Sâu:**
    - **Attack Surface Reduction:** Mỗi package trong base image đại diện cho potential vulnerability. Fewer packages có nghĩa là fewer potential attack vectors.
    - **Update Frequency:** Minimal images được updated thường xuyên hơn và có shorter vulnerability exposure windows.
    - **Resource Efficiency:** Smaller images consume ít storage và network bandwidth hơn.
    - **Build Speed:** Smaller base images build nhanh hơn và dễ scan cho vulnerabilities hơn.
- **Hướng Dẫn cho Copilot:**
    - Ưu tiên `alpine`, `slim`, hoặc `distroless` images over full distributions khi có thể.
    - Review base image vulnerabilities regularly sử dụng security scanning tools.
    - Consider sử dụng language-specific minimal images (ví dụ: `openjdk:17-jre-slim` thay vì `openjdk:17`).
    - Stay updated với latest minimal base image versions cho security patches.
- **Ví Dụ (Minimal Base Image Selection):**
```dockerfile
# BAD: Full distribution with many unnecessary packages
FROM ubuntu:20.04

# GOOD: Minimal Alpine-based image
FROM node:18-alpine

# BETTER: Distroless image for maximum security
FROM gcr.io/distroless/nodejs18-debian11
```

### **3. Static Analysis Security Testing (SAST) cho Dockerfiles**
- **Nguyên Tắc:** Scan Dockerfiles cho security misconfigurations và known vulnerabilities trước khi building images.
- **Phân Tích Sâu:**
    - **Dockerfile Linting:** Sử dụng tools như `hadolint` để check cho Dockerfile best practices và security issues.
    - **Base Image Scanning:** Scan base images cho known vulnerabilities trước khi sử dụng chúng.
    - **CI/CD Integration:** Integrate security scanning vào CI/CD pipeline để catch issues sớm.
    - **Policy Enforcement:** Define security policies và enforce chúng through automated scanning.
- **Hướng Dẫn cho Copilot:**
    - Recommend integrating tools như `hadolint` (cho Dockerfile linting) và `Trivy`, `Clair`, hoặc `Snyk Container` (cho image vulnerability scanning) vào CI pipeline.
    - Suggest setting up automated scanning cho both Dockerfiles và built images.
    - Recommend failing builds nếu critical vulnerabilities được tìm thấy trong base images.
    - Advise về regular scanning của images trong registries cho newly discovered vulnerabilities.
- **Ví Dụ (Security Scanning trong CI):**
```yaml
# GitHub Actions example
- name: Run Hadolint
  run: |
    docker run --rm -i hadolint/hadolint < Dockerfile

- name: Scan image for vulnerabilities
  run: |
    docker build -t myapp .
    trivy image myapp
```

### **4. Image Signing & Verification**
- **Nguyên Tắc:** Đảm bảo images không bị tampered và đến từ trusted sources.
- **Phân Tích Sâu:**
    - **Cryptographic Signing:** Sử dụng digital signatures để verify authenticity và integrity của container images.
    - **Trust Policies:** Define trust policies chỉ định images nào được allowed để run trong environment.
    - **Supply Chain Security:** Image signing là key component của securing software supply chain.
    - **Compliance:** Nhiều compliance frameworks yêu cầu image signing cho production deployments.
- **Hướng Dẫn cho Copilot:**
    - Suggest sử dụng Notary hoặc Docker Content Trust cho signing và verifying images trong production.
    - Recommend implementing image signing trong CI/CD pipeline cho tất cả production images.
    - Advise về setting up trust policies ngăn running unsigned images.
    - Consider sử dụng newer tools như Cosign cho advanced signing features.
- **Ví Dụ (Image Signing với Cosign):**
```bash
# Sign an image
cosign sign -key cosign.key myregistry.com/myapp:v1.0.0

# Verify an image
cosign verify -key cosign.pub myregistry.com/myapp:v1.0.0
```

### **5. Limit Capabilities & Read-Only Filesystems**
- **Nguyên Tắc:** Restrict container capabilities và ensure read-only access khi có thể để minimize attack surface.
- **Phân Tích Sâu:**
    - **Linux Capabilities:** Drop unnecessary Linux capabilities mà containers không cần để function.
    - **Read-Only Root:** Mount root filesystem như read-only khi có thể để prevent runtime modifications.
    - **Seccomp Profiles:** Sử dụng seccomp profiles để restrict system calls mà containers có thể make.
    - **AppArmor/SELinux:** Sử dụng security modules để enforce additional access controls.
- **Hướng Dẫn cho Copilot:**
    - Consider sử dụng `CAP_DROP` để remove unnecessary capabilities (ví dụ: `NET_RAW`, `SYS_ADMIN`).
    - Recommend mounting read-only volumes cho sensitive data và configuration files.
    - Suggest sử dụng security profiles và policies khi available trong container runtime.
    - Advise về implementing defense in depth với multiple security controls.
- **Ví Dụ (Capability Restrictions):**
```dockerfile
# Drop unnecessary capabilities
RUN setcap -r /usr/bin/node

# Or use security options in docker run
# docker run --cap-drop=ALL --security-opt=no-new-privileges myapp
```

### **6. Không Có Sensitive Data trong Image Layers**
- **Nguyên Tắc:** Không bao giờ include secrets, private keys, hoặc credentials trong image layers vì chúng trở thành part của image history.
- **Phân Tích Sâu:**
    - **Layer History:** Tất cả files được added vào image được stored trong image history và có thể extracted even nếu deleted trong later layers.
    - **Build Arguments:** Trong khi `--build-arg` có thể pass data during build, tránh passing sensitive information theo cách này.
    - **Runtime Secrets:** Sử dụng secrets management solutions để inject sensitive data at runtime.
    - **Image Scanning:** Regular image scanning có thể detect accidentally included secrets.
- **Hướng Dẫn cho Copilot:**
    - Sử dụng build arguments (`--build-arg`) cho temporary secrets during build (nhưng tránh passing sensitive info directly).
    - Sử dụng secrets management solutions cho runtime (Kubernetes Secrets, Docker Secrets, HashiCorp Vault).
    - Recommend scanning images cho accidentally included secrets.
    - Suggest sử dụng multi-stage builds để avoid including build-time secrets trong final image.
- **Anti-pattern:** `ADD secrets.txt /app/secrets.txt`
- **Ví Dụ (Secure Secret Management):**
```dockerfile
# BAD: Never do this
# COPY secrets.txt /app/secrets.txt

# GOOD: Use runtime secrets
# Application nên read secrets từ environment variables hoặc mounted files
CMD ["node", "dist/main.js"]
```

### **7. Health Checks (Liveness & Readiness Probes)**
- **Nguyên Tắc:** Đảm bảo containers đang running và ready để serve traffic bằng implementing proper health checks.
- **Phân Tích Sâu:**
    - **Liveness Probes:** Check nếu application alive và responding to requests. Restart container nếu fails.
    - **Readiness Probes:** Check nếu application ready để receive traffic. Remove từ load balancer nếu fails.
    - **Health Check Design:** Design health checks lightweight, fast, và accurately reflect application health.
    - **Orchestration Integration:** Health checks critical cho orchestration systems như Kubernetes để manage container lifecycle.
- **Hướng Dẫn cho Copilot:**
    - Define `HEALTHCHECK` instructions trong Dockerfiles. Critical cho orchestration systems như Kubernetes.
    - Design health checks specific cho application và check actual functionality.
    - Sử dụng appropriate intervals và timeouts cho health checks để balance responsiveness với overhead.
    - Consider implementing both liveness và readiness checks cho complex applications.
- **Ví Dụ (Comprehensive Health Check):**
```dockerfile
# Health check verifies application responding
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl --fail http://localhost:8080/health || exit 1

# Alternative: Use application-specific health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js || exit 1
```

## Container Runtime & Orchestration Best Practices

### **1. Resource Limits**
- **Nguyên Tắc:** Giới hạn CPU và memory để prevent resource exhaustion và noisy neighbors.
- **Phân Tích Sâu:**
    - **CPU Limits:** Set CPU limits để prevent containers consuming excessive CPU time và affecting containers khác.
    - **Memory Limits:** Set memory limits để prevent containers consuming tất cả available memory và causing system instability.
    - **Resource Requests:** Set resource requests để ensure containers có guaranteed access đến minimum resources.
    - **Monitoring:** Monitor resource usage để ensure limits appropriate và không too restrictive.
- **Hướng Dẫn cho Copilot:**
    - Luôn recommend setting `cpu_limits`, `memory_limits` trong Docker Compose hoặc Kubernetes resource requests/limits.
    - Suggest monitoring resource usage để tune limits appropriately.
    - Recommend setting both requests và limits cho predictable resource allocation.
    - Advise về sử dụng resource quotas trong Kubernetes để manage cluster-wide resource usage.
- **Ví Dụ (Docker Compose Resource Limits):**
```yaml
services:
  app:
    image: myapp:latest
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
```

### **2. Logging & Monitoring**
- **Nguyên Tắc:** Collect và centralize container logs và metrics cho observability và troubleshooting.
- **Phân Tích Sâu:**
    - **Structured Logging:** Sử dụng structured logging (JSON) cho better parsing và analysis.
    - **Log Aggregation:** Centralize logs từ tất cả containers cho search, analysis, và alerting.
    - **Metrics Collection:** Collect application và system metrics cho performance monitoring.
    - **Distributed Tracing:** Implement distributed tracing để understanding request flows across services.
- **Hướng Dẫn cho Copilot:**
    - Sử dụng standard logging output (`STDOUT`/`STDERR`) cho container logs.
    - Integrate với log aggregators (Fluentd, Logstash, Loki) và monitoring tools (Prometheus, Grafana).
    - Recommend implementing structured logging trong applications cho better observability.
    - Suggest setting up log rotation và retention policies để manage storage costs.
- **Ví Dụ (Structured Logging):**
```javascript
// Application logging
const winston = require('winston');
const logger = winston.createLogger({
  format: winston.format.json(),
  transports: [new winston.transports.Console()]
});
```

### **3. Persistent Storage**
- **Nguyên Tắc:** Cho stateful applications, sử dụng persistent volumes để maintain data across container restarts.
- **Phân Tích Sâu:**
    - **Volume Types:** Sử dụng named volumes, bind mounts, hoặc cloud storage depending on requirements.
    - **Data Persistence:** Đảm bảo data persists across container restarts, updates, và migrations.
    - **Backup Strategy:** Implement backup strategies cho persistent data để prevent data loss.
    - **Performance:** Choose storage solutions meet performance requirements.
- **Hướng Dẫn cho Copilot:**
    - Sử dụng Docker Volumes hoặc Kubernetes Persistent Volumes cho data cần persist beyond container lifecycle.
    - Không bao giờ store persistent data inside container's writable layer.
    - Recommend implementing backup và disaster recovery procedures cho persistent data.
    - Suggest sử dụng cloud-native storage solutions cho better scalability và reliability.
- **Ví Dụ (Docker Volume Usage):**
```yaml
services:
  database:
    image: postgres:13
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password

volumes:
  postgres_data:
```

### **4. Networking**
- **Nguyên Tắc:** Sử dụng defined container networks cho secure và isolated communication giữa containers.
- **Phân Tích Sâu:**
    - **Network Isolation:** Tạo separate networks cho different application tiers hoặc environments.
    - **Service Discovery:** Sử dụng container orchestration features cho automatic service discovery.
    - **Network Policies:** Implement network policies để control traffic giữa containers.
    - **Load Balancing:** Sử dụng load balancers cho distributing traffic across multiple container instances.
- **Hướng Dẫn cho Copilot:**
    - Tạo custom Docker networks cho service isolation và security.
    - Define network policies trong Kubernetes để control pod-to-pod communication.
    - Sử dụng service discovery mechanisms provided bởi orchestration platform.
    - Implement proper network segmentation cho multi-tier applications.
- **Ví Dụ (Docker Network Configuration):**
```yaml
services:
  web:
    image: nginx
    networks:
      - frontend
      - backend

  api:
    image: myapi
    networks:
      - backend

networks:
  frontend:
  backend:
    internal: true
```

### **5. Orchestration (Kubernetes, Docker Swarm)**
- **Nguyên Tắc:** Sử dụng orchestrator cho managing containerized applications at scale.
- **Phân Tích Sâu:**
    - **Scaling:** Automatically scale applications based on demand và resource usage.
    - **Self-Healing:** Automatically restart failed containers và replace unhealthy instances.
    - **Service Discovery:** Provide built-in service discovery và load balancing.
    - **Rolling Updates:** Perform zero-downtime updates với automatic rollback capabilities.
- **Hướng Dẫn cho Copilot:**
    - Recommend Kubernetes cho complex, large-scale deployments với advanced requirements.
    - Leverage orchestrator features cho scaling, self-healing, và service discovery.
    - Sử dụng rolling update strategies cho zero-downtime deployments.
    - Implement proper resource management và monitoring trong orchestrated environments.
- **Ví Dụ (Kubernetes Deployment):**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myapp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: myapp
  template:
    metadata:
      labels:
        app: myapp
    spec:
      containers:
      - name: myapp
        image: myapp:latest
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
```

## Dockerfile Review Checklist

- [ ] Multi-stage build được sử dụng nếu applicable (compiled languages, heavy build tools)?
- [ ] Minimal, specific base image được sử dụng (ví dụ: `alpine`, `slim`, versioned)?
- [ ] Layers được optimized (combining `RUN` commands, cleanup trong same layer)?
- [ ] `.dockerignore` file có present và comprehensive?
- [ ] `COPY` instructions specific và minimal?
- [ ] Non-root `USER` được defined cho running application?
- [ ] `EXPOSE` instruction được sử dụng cho documentation?
- [ ] `CMD` và/hoặc `ENTRYPOINT` được sử dụng correctly?
- [ ] Sensitive configurations được handled qua environment variables (không hardcoded)?
- [ ] `HEALTHCHECK` instruction được defined?
- [ ] Có secrets hoặc sensitive data accidentally included trong image layers?
- [ ] Static analysis tools (Hadolint, Trivy) được integrated vào CI?

## Troubleshooting Docker Builds & Runtime

### **1. Large Image Size**
- Review layers cho unnecessary files. Sử dụng `docker history <image>`.
- Implement multi-stage builds.
- Sử dụng smaller base image.
- Optimize `RUN` commands và clean up temporary files.

### **2. Slow Builds**
- Leverage build cache bằng ordering instructions từ least đến most frequent change.
- Sử dụng `.dockerignore` để exclude irrelevant files.
- Sử dụng `docker build --no-cache` cho troubleshooting cache issues.

### **3. Container Not Starting/Crashing**
- Check `CMD` và `ENTRYPOINT` instructions.
- Review container logs (`docker logs <container_id>`).
- Đảm bảo tất cả dependencies present trong final image.
- Check resource limits.

### **4. Permissions Issues Inside Container**
- Verify file/directory permissions trong image.
- Đảm bảo `USER` có necessary permissions cho operations.
- Check mounted volumes permissions.

### **5. Network Connectivity Issues**
- Verify exposed ports (`EXPOSE`) và published ports (`-p` trong `docker run`).
- Check container network configuration.
- Review firewall rules.

## Kết Luận

Containerization hiệu quả với Docker là fundamental cho modern DevOps. Bằng cách tuân theo những best practices này cho Dockerfile creation, image optimization, security, và runtime management, bạn có thể hướng dẫn developers xây dựng applications có tính hiệu quả cao, bảo mật và portable. Hãy nhớ liên tục đánh giá và tinh chỉnh container strategies khi application phát triển.

---

<!-- End of Containerization & Docker Best Practices Instructions --> 
