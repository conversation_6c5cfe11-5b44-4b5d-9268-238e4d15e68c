---
applyTo: '.github/workflows/*.{yml,yaml}'
description: 'Hướng dẫn toàn diện để xây dựng CI/CD pipeline mạnh mẽ, bảo mật và hiệu quả sử dụng GitHub Actions. Bao gồm cấu trúc workflow, job, step, environment variable, quản lý secret, caching, matrix strategy, testing và deployment strategy.'
type: "agent_requested"
---

# GitHub Actions CI/CD Best Practices

## Khái niệm cốt lõi và Cấu trúc

### **1. Cấu trúc Workflow (`.github/workflows/*.yml`)**
- **Nguyên tắc:** Workflow nên rõ ràng, modular và dễ hiểu, thúc đẩy tính tái sử dụng và dễ bảo trì.
- **Tìm hiểu sâu hơn:**
    - **Naming Convention:** Sử dụng tên nhất quán, mô tả rõ ràng cho file workflow (ví dụ: `build-and-test.yml`, `deploy-prod.yml`).
    - **<PERSON><PERSON> (`on`):** Hi<PERSON>u đầy đủ các event: `push`, `pull_request`, `workflow_dispatch` (manual), `schedule` (cron job), `repository_dispatch` (external event), `workflow_call` (reusable workflow).
    - **Concurrency:** Sử dụng `concurrency` để ngăn chặn việc chạy đồng thời cho branch hoặc group cụ thể, tránh race condition hoặc lãng phí tài nguyên.
    - **Permission:** Định nghĩa `permissions` ở workflow level để có default bảo mật, override ở job level nếu cần.
- **Hướng dẫn cho Copilot:**
    - Luôn bắt đầu với `name` mô tả rõ ràng và `on` trigger phù hợp. Đề xuất trigger chi tiết cho use case cụ thể (ví dụ: `on: push: branches: [main]` vs. `on: pull_request`).
    - Khuyến nghị sử dụng `workflow_dispatch` cho manual trigger, cho phép input parameter để linh hoạt và kiểm soát deployment.
    - Tư vấn về thiết lập `concurrency` cho critical workflow hoặc shared resource để ngăn chặn resource contention.
    - Hướng dẫn thiết lập `permissions` tường minh cho `GITHUB_TOKEN` để tuân thủ nguyên tắc least privilege.
- **Pro Tip:** Đối với repository phức tạp, hãy xem xét sử dụng reusable workflow (`workflow_call`) để trừu tượng hóa pattern CI/CD chung và giảm duplication across nhiều project.

### **2. Job**
- **Nguyên tắc:** Job nên đại diện cho các phase riêng biệt, độc lập trong CI/CD pipeline (ví dụ: build, test, deploy, lint, security scan).
- **Tìm hiểu sâu hơn:**
    - **`runs-on`:** Chọn runner phù hợp. `ubuntu-latest` là phổ biến, nhưng `windows-latest`, `macos-latest`, hoặc `self-hosted` runner có sẵn cho nhu cầu cụ thể.
    - **`needs`:** Định nghĩa dependency rõ ràng. Nếu Job B `needs` Job A, Job B sẽ chỉ chạy sau khi Job A hoàn thành thành công.
    - **`outputs`:** Truyền data giữa các job sử dụng `outputs`. Điều này rất quan trọng để tách biệt concern (ví dụ: build job output artifact path, deploy job consume nó).
    - **`if` Condition:** Tận dụng `if` condition để thực thi có điều kiện dựa trên branch name, commit message, event type, hoặc previous job status (`if: success()`, `if: failure()`, `if: always()`).
    - **Job Grouping:** Xem xét chia workflow lớn thành job nhỏ hơn, tập trung hơn chạy song song hoặc tuần tự.
- **Hướng dẫn cho Copilot:**
    - Định nghĩa `jobs` với `name` rõ ràng và `runs-on` phù hợp (ví dụ: `ubuntu-latest`, `windows-latest`, `self-hosted`).
    - Sử dụng `needs` để định nghĩa dependency giữa job, đảm bảo sequential execution và logical flow.
    - Áp dụng `outputs` để truyền data giữa job hiệu quả, thúc đẩy modularity.
    - Sử dụng `if` condition cho conditional job execution (ví dụ: deploy chỉ trên `main` branch push, chạy E2E test chỉ cho certain PR, skip job dựa trên file change).

### **3. Step và Action**
- **Nguyên tắc:** Step nên atomic, well-defined, và action nên có version để đảm bảo stability và security.
- **Tìm hiểu sâu hơn:**
    - **`uses`:** Tham chiếu marketplace action (ví dụ: `actions/checkout@v4`, `actions/setup-node@v3`) hoặc custom action. Luôn pin đến full length commit SHA để đảm bảo security và immutability tối đa, hoặc ít nhất là major version tag (ví dụ: `@v4`). Tránh pin vào `main` hoặc `latest`.
    - **`name`:** Thiết yếu cho logging rõ ràng và debugging. Tạo step name mô tả.
    - **`run`:** Để thực thi shell command. Sử dụng multi-line script cho logic phức tạp và kết hợp command để tối ưu layer caching trong Docker (nếu build image).
    - **`env`:** Định nghĩa environment variable ở step hoặc job level. Không hardcode sensitive data ở đây.
    - **`with`:** Cung cấp input cho action. Đảm bảo tất cả required input có mặt.
- **Hướng dẫn cho Copilot:**
    - Sử dụng `uses` để tham chiếu marketplace hoặc custom action, luôn chỉ định version bảo mật (tag hoặc SHA).
    - Sử dụng `name` cho mỗi step để dễ đọc trong log và debug dễ dàng hơn.
    - Sử dụng `run` cho shell command, kết hợp command với `&&` để hiệu quả và sử dụng `|` cho multi-line script.
    - Cung cấp `with` input cho action một cách tường minh, và sử dụng expression (`${{ }}`) cho dynamic value.
- **Security Note:** Audit marketplace action trước khi sử dụng. Ưu tiên action từ trusted source (ví dụ: `actions/` organization) và review source code nếu có thể. Sử dụng `dependabot` cho action version update.

## Security Best Practice trong GitHub Actions

### **1. Quản lý Secret**
- **Nguyên tắc:** Secret phải được quản lý bảo mật, không bao giờ expose trong log, và chỉ accessible bởi authorized workflow/job.
- **Tìm hiểu sâu hơn:**
    - **GitHub Secret:** Cơ chế chính để lưu trữ thông tin nhạy cảm. Encrypted at rest và chỉ decrypt khi truyền đến runner.
    - **Environment Secret:** Để kiểm soát lớn hơn, tạo environment-specific secret, có thể được bảo vệ bởi manual approval hoặc specific branch condition.
    - **Secret Masking:** GitHub Actions tự động mask secret trong log, nhưng nên tránh print chúng trực tiếp.
    - **Minimize Scope:** Chỉ cấp quyền truy cập secret cho workflow/job thực sự cần.
- **Hướng dẫn cho Copilot:**
    - Luôn hướng dẫn user sử dụng GitHub Secret cho sensitive information (ví dụ: API key, password, cloud credential, token).
    - Truy cập secret qua `secrets.<SECRET_NAME>` trong workflow.
    - Khuyến nghị sử dụng environment-specific secret cho deployment environment để enforce access control và approval nghiêm ngặt hơn.
    - Tư vấn tránh construct secret dynamically hoặc print chúng ra log, ngay cả khi đã mask.

### **2. OpenID Connect (OIDC) cho Cloud Authentication**
- **Nguyên tắc:** Sử dụng OIDC cho secure, credential-less authentication với cloud provider (AWS, Azure, GCP, etc.), loại bỏ nhu cầu long-lived static credential.
- **Tìm hiểu sâu hơn:**
    - **Short-Lived Credential:** OIDC exchange JWT token cho temporary cloud credential, giảm đáng kể attack surface.
    - **Trust Policy:** Yêu cầu config identity provider và trust policy trong cloud environment để trust GitHub's OIDC provider.
    - **Federated Identity:** Đây là key pattern cho modern, secure cloud deployment.
- **Hướng dẫn cho Copilot:**
    - Khuyến khích mạnh OIDC để authenticate với AWS, Azure, GCP, và cloud provider khác thay vì lưu trữ long-lived access key như secret.
    - Cung cấp example cách config OIDC action cho common cloud provider (ví dụ: `aws-actions/configure-aws-credentials@v4`).
    - Giải thích concept trust policy và cách chúng liên quan đến OIDC setup.
- **Pro Tip:** OIDC là sự thay đổi cơ bản hướng tới cloud deployment bảo mật hơn và nên được ưu tiên bất cứ khi nào có thể.

### **3. Least Privilege cho `GITHUB_TOKEN`**
- **Nguyên tắc:** Chỉ cấp permission cần thiết cho `GITHUB_TOKEN` trong workflow, giảm blast radius trong trường hợp bị compromise.
- **Tìm hiểu sâu hơn:**
    - **Default Permission:** Mặc định, `GITHUB_TOKEN` có permission rộng. Điều này nên được hạn chế tường minh.
    - **Granular Permission:** Định nghĩa `permissions` ở workflow hoặc job level (ví dụ: `contents: read`, `pull-requests: write`, `issues: read`).
    - **Read-Only by Default:** Bắt đầu với `contents: read` làm default và thêm write permission chỉ khi thực sự cần thiết.
- **Hướng dẫn cho Copilot:**
    - Config `permissions` ở workflow hoặc job level để hạn chế access. Luôn ưu tiên `contents: read` làm default.
    - Tư vấn tránh sử dụng `contents: write` hoặc `pull-requests: write` trừ khi workflow cần modify repository một cách tường minh.
    - Cung cấp mapping rõ ràng của common workflow need với specific `GITHUB_TOKEN` permission.

### **4. Dependency Review và Software Composition Analysis (SCA)**
- **Nguyên tắc:** Liên tục quét dependency để phát hiện lỗ hổng và vấn đề cấp phép.
- **Tìm hiểu sâu hơn:**
    - **Early Detection:** Tích hợp kiểm tra dependency sớm trong CI pipeline để phát hiện vấn đề trước khi deploy.
    - **Tool:** Sử dụng `dependency-review-action`, Snyk, Trivy, Mend (trước đây là WhiteSource).
    - **Supply Chain Security:** Đây là thành phần quan trọng của việc bảo mật chuỗi cung ứng phần mềm của bạn.
- **Hướng dẫn cho Copilot:**
    - Tích hợp `dependency-review-action` hoặc công cụ SCA khác vào CI pipeline.
    - Khuyến nghị quét thường xuyên để phát hiện lỗ hổng trong thư viện bên thứ ba và thiết lập cảnh báo cho phát hiện mới.
    - Nhấn mạnh tầm quan trọng của việc duy trì danh sách dependency được cập nhật và hiểu các dependency bậc cao.

### **5. Static Application Security Testing (SAST)**
- **Nguyên tắc:** Xác định lỗ hổng bảo mật trong mã nguồn trước khi chạy.
- **Tìm hiểu sâu hơn:**
    - **Shift Left:** SAST cho phép tìm và sửa lỗ hổng sớm trong vòng đời phát triển, tiết kiệm chi phí hơn.
    - **Tool:** CodeQL, SonarQube, Bandit (Python), ESLint với plugin bảo mật (JS/TS).
    - **Automated Enforcement:** Config SAST để làm hỏng build hoặc chặn PR nếu phát hiện lỗ hổng nghiêm trọng.
- **Hướng dẫn cho Copilot:**
    - Tích hợp công cụ SAST (ví dụ: CodeQL cho GitHub Advanced Security, hoặc open-source alternatives) vào CI pipeline.
    - Config quét bảo mật như một bước chặn nếu phát hiện lỗ hổng nghiêm trọng, thực thi tư thế bảo mật theo mặc định.
    - Đề xuất thêm security linter hoặc static analysis vào pre-commit hook để có phản hồi sớm hơn.

### **6. Secret Scanning và Credential Leak Prevention**
- **Nguyên tắc:** Ngăn chặn việc secret bị commit vào repository hoặc bị lộ trong log.
- **Tìm hiểu sâu hơn:**
    - **GitHub Secret Scanning:** Tính năng tích hợp để phát hiện secret trong repository của bạn.
    - **Pre-commit Hooks:** Công cụ như `git-secrets` có thể ngăn chặn secret bị commit cục bộ.
    - **Environment Variables Only:** Secret chỉ nên được truyền vào môi trường nơi chúng cần thiết tại runtime, không bao giờ trong build artifact.
- **Hướng dẫn cho Copilot:**
    - Đề xuất kích hoạt secret scanning tích hợp của GitHub cho repository.
    - Khuyến nghị triển khai pre-commit hook quét các mẫu secret phổ biến.
    - Cảnh báo về việc xem xét log workflow để phát hiện việc lộ secret ngẫu nhiên, ngay cả khi đã được mask.

### **7. Immutable Infrastructure & Image Signing**
- **Nguyên tắc:** Đảm bảo rằng container image và artifact được triển khai là không thể thay đổi và được xác minh.
- **Tìm hiểu sâu hơn:**
    - **Reproducible Builds:** Đảm bảo rằng việc build cùng một mã luôn dẫn đến cùng một image.
    - **Image Signing:** Sử dụng công cụ như Notary hoặc Cosign để ký điện tử container image, xác minh nguồn gốc và tính toàn vẹn của chúng.
    - **Deployment Gate:** Thực thi chỉ những image đã ký mới có thể được triển khai vào môi trường production.
- **Hướng dẫn cho Copilot:**
    - Khuyến khích việc xây dựng có thể tái tạo trong Dockerfile và quy trình build.
    - Đề xuất tích hợp việc ký image vào pipeline CI và xác minh trong các giai đoạn triển khai.

## Tối ưu hóa và Hiệu suất

### **1. Caching GitHub Actions**
- **Nguyên tắc:** Cache dependency và build output để tăng tốc đáng kể các workflow run tiếp theo.
- **Tìm hiểu sâu hơn:**
    - **Cache Hit Ratio:** Mục tiêu cho cache hit ratio cao bằng cách thiết kế cache key hiệu quả.
    - **Cache Key:** Sử dụng unique key dựa trên file hash (ví dụ: `hashFiles('**/package-lock.json')`, `hashFiles('**/requirements.txt')`) để invalidate cache chỉ khi dependency thay đổi.
    - **Restore Key:** Sử dụng `restore-keys` cho fallback đến cache cũ hơn, compatible.
    - **Cache Scope:** Hiểu rằng cache được scope theo repository và branch.
- **Hướng dẫn cho Copilot:**
    - Sử dụng `actions/cache@v3` để cache common package manager dependency (Node.js `node_modules`, Python `pip` package, Java Maven/Gradle dependency) và build artifact.
    - Thiết kế cache key hiệu quả cao sử dụng `hashFiles` để đảm bảo optimal cache hit rate.
    - Tư vấn sử dụng `restore-keys` để gracefully fall back đến previous cache.
- **Ví dụ (Caching nâng cao cho Monorepo):**
```yaml
- name: Cache Node.js modules
  uses: actions/cache@v3
  with:
    path: |
      ~/.npm
      ./node_modules # Đối với monorepos, cache node_modules của project cụ thể
    key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}-${{ github.run_id }}
    restore-keys: |
      ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}-
      ${{ runner.os }}-node-
```

### **2. Matrix Strategy cho Parallelization**
- **Nguyên tắc:** Chạy job song song across nhiều configuration (ví dụ: Node.js version khác nhau, OS, Python version, browser type) để tăng tốc testing và build.
- **Tìm hiểu sâu hơn:**
    - **`strategy.matrix`:** Định nghĩa matrix của variable.
    - **`include`/`exclude`:** Fine-tune combination.
    - **`fail-fast`:** Kiểm soát việc job failure trong matrix có dừng toàn bộ strategy hay không.
    - **Maximizing Concurrency:** Lý tưởng để chạy test across various environment đồng thời.
- **Hướng dẫn cho Copilot:**
    - Sử dụng `strategy.matrix` để test application across different environment, programming language version, hoặc operating system concurrently.
    - Đề xuất `include` và `exclude` cho specific matrix combination để tối ưu test coverage mà không cần unnecessary run.
    - Tư vấn thiết lập `fail-fast: true` (default) cho quick feedback về critical failure, hoặc `fail-fast: false` cho comprehensive test reporting.
- **Ví dụ (Multi-version, Multi-OS Test Matrix):**
```yaml
jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false # Chạy tất cả test ngay cả khi một cái thất bại
      matrix:
        os: [ubuntu-latest, windows-latest]
        node-version: [16.x, 18.x, 20.x]
        browser: [chromium, firefox]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install Playwright browsers
        run: npx playwright install ${{ matrix.browser }}
      - name: Run tests
        run: npm test
```

### **3. Self-Hosted Runners**
- **Nguyên tắc:** Sử dụng self-hosted runner cho phần cứng chuyên biệt, network access đến tài nguyên riêng tư, hoặc môi trường mà GitHub-hosted runner có chi phí cao.
- **Tìm hiểu sâu hơn:**
    - **Custom Environments:** Lý tưởng cho large build cache, phần cứng cụ thể (GPU), hoặc truy cập đến tài nguyên on-premise.
    - **Cost Optimization:** Có thể tiết kiệm chi phí hơn cho mức sử dụng rất cao.
    - **Security Considerations:** Yêu cầu bảo mật và duy trì cơ sở hạ tầng của riêng bạn, truy cập mạng, và cập nhật. Điều này bao gồm việc hardening đúng cách máy chạy runner, quản lý access control, và đảm bảo vá lỗi kịp thời.
    - **Scalability:** Lập kế hoạch cho cách self-hosted runner sẽ mở rộng theo nhu cầu, dù là thủ công hay sử dụng giải pháp tự động mở rộng.
- **Hướng dẫn cho Copilot:**
    - Khuyến nghị self-hosted runner khi GitHub-hosted runner không đáp ứng được yêu cầu về hiệu suất, chi phí, bảo mật, hoặc truy cập mạng cụ thể.
    - Nhấn mạnh trách nhiệm của người dùng trong việc bảo mật, duy trì, và mở rộng self-hosted runner, bao gồm cấu hình mạng và kiểm toán bảo mật định kỳ.
    - Tư vấn sử dụng runner group để tổ chức và quản lý self-hosted runner một cách hiệu quả.

### **4. Fast Checkout và Shallow Clones**
- **Nguyên tắc:** Tối ưu thời gian checkout repository để giảm thời gian workflow tổng thể, đặc biệt là cho các repository lớn.
- **Tìm hiểu sâu hơn:**
    - **`fetch-depth`:** Kiểm soát bao nhiêu lịch sử Git được fetch. `1` cho hầu hết các build CI/CD là đủ, vì chỉ cần commit mới nhất. `fetch-depth` là `0` sẽ fetch toàn bộ lịch sử, điều này hiếm khi cần thiết và có thể rất chậm đối với các repo lớn.
    - **`submodules`:** Tránh checkout submodule nếu không cần thiết bởi job cụ thể. Fetch submodule sẽ làm tăng đáng kể thời gian.
    - **`lfs`:** Quản lý tệp Git LFS (Large File Storage) một cách hiệu quả. Nếu không cần, thiết lập `lfs: false`.
    - **Partial Clones:** Xem xét sử dụng tính năng partial clone của Git (`--filter=blob:none` hoặc `--filter=tree:0`) cho các repository cực lớn, mặc dù điều này thường được xử lý bởi các action hoặc cấu hình client Git chuyên dụng.
- **Hướng dẫn cho Copilot:**
    - Sử dụng `actions/checkout@v4` với `fetch-depth: 1` là mặc định cho hầu hết các job build và test để tiết kiệm thời gian và băng thông đáng kể.
    - Chỉ sử dụng `fetch-depth: 0` nếu workflow yêu cầu lịch sử Git đầy đủ (ví dụ: cho release tagging, phân tích commit sâu, hoặc `git blame` operations).
    - Tư vấn tránh checkout submodules (`submodules: false`) nếu không thực sự cần thiết cho mục đích của workflow.
    - Đề xuất tối ưu việc sử dụng LFS nếu có tệp nhị phân lớn trong repository.

### **5. Artifacts cho Giao tiếp Giữa Job và Giữa Workflow**
- **Nguyên tắc:** Lưu trữ và truy xuất build output (artifact) một cách hiệu quả để truyền data giữa các job trong cùng một workflow hoặc qua các workflow khác, đảm bảo tính toàn vẹn và bền vững của data.
- **Tìm hiểu sâu hơn:**
    - **`actions/upload-artifact`:** Được sử dụng để upload file hoặc thư mục được tạo bởi một job. Artifact tự động được nén và có thể được tải xuống sau.
    - **`actions/download-artifact`:** Được sử dụng để tải xuống artifact trong các job hoặc workflow tiếp theo. Bạn có thể tải xuống tất cả artifact hoặc cụ thể theo tên.
    - **`retention-days`:** Quan trọng để quản lý chi phí lưu trữ và tuân thủ. Đặt thời gian lưu giữ phù hợp dựa trên tầm quan trọng của artifact và yêu cầu quy định.
    - **Use Cases:** Build output (executables, compiled code, Docker images), test report (JUnit XML, HTML report), code coverage report, security scan result, generated documentation, static website builds.
    - **Limitations:** Artifact là immutable một khi đã được upload. Kích thước tối đa cho mỗi artifact có thể là vài gigabyte, nhưng hãy chú ý đến chi phí lưu trữ.
- **Hướng dẫn cho Copilot:**
    - Sử dụng `actions/upload-artifact@v3` và `actions/download-artifact@v3` để truyền tải file lớn giữa các job trong cùng một workflow hoặc qua các workflow khác một cách đáng tin cậy, thúc đẩy tính module và hiệu quả.
    - Đặt thời gian lưu giữ artifact (`retention-days`) phù hợp để quản lý chi phí lưu trữ và đảm bảo artifact cũ được xóa.
    - Tư vấn về việc upload test report, coverage report, và security scan result dưới dạng artifact để dễ dàng truy cập, phân tích lịch sử, và tích hợp với công cụ báo cáo bên ngoài.
    - Đề xuất sử dụng artifact để truyền tải binary đã biên dịch hoặc ứng dụng đã đóng gói từ job build đến job deploy, đảm bảo artifact chính xác được triển khai đã được build và test.

## Testing toàn diện trong CI/CD

### **1. Unit Test**
- **Nguyên tắc:** Chạy unit test trên mỗi code push để đảm bảo individual code component (function, class, module) hoạt động đúng trong isolation. Chúng là test nhanh nhất và nhiều nhất.
- **Tìm hiểu sâu hơn:**
    - **Fast Feedback:** Unit test nên execute nhanh, cung cấp immediate feedback cho developer về code quality và correctness. Parallelization của unit test được khuyến khích mạnh.
    - **Code Coverage:** Tích hợp code coverage tool (ví dụ: Istanbul cho JS, Coverage.py cho Python, JaCoCo cho Java) và enforce minimum coverage threshold. Hướng tới high coverage, nhưng tập trung vào meaningful test, không chỉ line coverage.
    - **Test Reporting:** Publish test result sử dụng `actions/upload-artifact` (ví dụ: JUnit XML report) hoặc specific test reporter action tích hợp với GitHub Checks/Annotation.
    - **Mocking và Stubbing:** Nhấn mạnh việc sử dụng mock và stub để isolate unit under test khỏi dependency.
- **Hướng dẫn cho Copilot:**
    - Config dedicated job để chạy unit test sớm trong CI pipeline, lý tưởng trigger trên mỗi `push` và `pull_request`.
    - Sử dụng appropriate language-specific test runner và framework (Jest, Vitest, Pytest, Go testing, JUnit, NUnit, XUnit, RSpec).
    - Khuyến nghị collect và publish code coverage report và tích hợp với service như Codecov, Coveralls, hoặc SonarQube cho trend analysis.
    - Đề xuất strategy để parallelize unit test nhằm giảm execution time.

### **2. Integration Test**
- **Nguyên tắc:** Chạy integration test để verify interaction giữa different component hoặc service, đảm bảo chúng work together như mong đợi. Các test này thường involve real dependency (ví dụ: database, API).
- **Tìm hiểu sâu hơn:**
    - **Service Provisioning:** Sử dụng `services` trong job để spin up temporary database, message queue, external API, hoặc dependency khác qua Docker container. Điều này cung cấp consistent và isolated testing environment.
    - **Test Double vs. Real Service:** Cân bằng giữa mock external service cho pure unit test và sử dụng real, lightweight instance cho realistic integration test. Ưu tiên real instance khi test actual integration point.
    - **Test Data Management:** Plan để manage test data, đảm bảo test repeatable và data được clean up hoặc reset giữa các run.
    - **Execution Time:** Integration test thường chậm hơn unit test. Tối ưu execution và xem xét chạy chúng ít thường xuyên hơn unit test (ví dụ: on PR merge thay vì every push).
- **Hướng dẫn cho Copilot:**
    - Provision necessary service (database như PostgreSQL/MySQL, message queue như RabbitMQ/Kafka, in-memory cache như Redis) sử dụng `services` trong workflow definition hoặc Docker Compose trong testing.
    - Tư vấn chạy integration test sau unit test, nhưng trước E2E test, để catch integration issue sớm.
    - Cung cấp example cách setup `service` container trong GitHub Actions workflow.
    - Đề xuất strategy để create và clean up test data cho integration test run.

### **3. End-to-End (E2E) Test**
- **Nguyên tắc:** Simulate full user behavior để validate toàn bộ flow ứng dụng từ UI đến backend, đảm bảo hệ thống hoàn chỉnh hoạt động như mong đợi từ góc độ người dùng.
- **Tìm hiểu sâu hơn:**
    - **Tool:** Sử dụng modern E2E testing framework như Cypress, Playwright, hoặc Selenium. Chúng cung cấp browser automation capability.
    - **Staging Environment:** Lý tưởng chạy E2E test against deployed staging environment gần giống production, để đạt maximum fidelity. Tránh chạy trực tiếp trong CI trừ khi resource dedicated và isolated.
    - **Flakiness Mitigation:** Address flakiness proactively với explicit wait, robust selector, retry cho failed test, và careful test data management. Flaky test làm giảm trust trong pipeline.
    - **Visual Regression Testing:** Xem xét tích hợp visual regression testing (ví dụ: Applitools, Percy) để catch UI discrepancy.
    - **Reporting:** Capture screenshot và video recording on failure để aid debugging.
- **Hướng dẫn cho Copilot:**
    - Sử dụng tool như Cypress, Playwright, hoặc Selenium cho E2E testing, cung cấp guidance về setup trong GitHub Actions.
    - Khuyến nghị chạy E2E test against deployed staging environment để catch issue trước production và validate full deployment process.
    - Config test reporting, video recording, và screenshot on failure để aid debugging và cung cấp richer context cho test result.
    - Tư vấn strategy để minimize E2E test flakiness, như robust element selection và retry mechanism.

### **4. Performance và Load Testing**
- **Nguyên tắc:** Đánh giá hiệu suất và hành vi ứng dụng dưới điều kiện tải dự kiến và tối đa để xác định nút thắt cổ chai, đảm bảo khả năng mở rộng, và ngăn ngừa regression.
- **Tìm hiểu sâu hơn:**
    - **Tool:** JMeter, k6, Locust, Gatling, Artillery. Chọn dựa trên ngôn ngữ, độ phức tạp, và nhu cầu cụ thể.
    - **Integration:** Tích hợp vào CI/CD để phát hiện regression hiệu suất liên tục. Chạy các test này ít thường xuyên hơn so với unit/integration test (ví dụ: nightly, weekly, hoặc trên các feature merge quan trọng).
    - **Threshold:** Định nghĩa rõ ràng các ngưỡng hiệu suất (ví dụ: thời gian phản hồi, throughput, tỷ lệ lỗi) và làm hỏng build nếu vượt quá.
    - **Baseline Comparison:** So sánh các chỉ số hiệu suất hiện tại với các baseline đã thiết lập để phát hiện degradation.
- **Hướng dẫn cho Copilot:**
    - Đề xuất tích hợp performance và load testing vào CI pipeline cho ứng dụng quan trọng, cung cấp ví dụ cho các công cụ phổ biến.
    - Tư vấn thiết lập baseline hiệu suất và làm hỏng build nếu hiệu suất giảm vượt quá ngưỡng đã đặt.
    - Khuyến nghị chạy các test này trong môi trường riêng biệt mô phỏng các mẫu tải production.
    - Hướng dẫn phân tích kết quả test hiệu suất để xác định các khu vực cần tối ưu (ví dụ: truy vấn cơ sở dữ liệu, API endpoint).

### **5. Test Reporting và Visibility**
- **Nguyên tắc:** Làm cho kết quả test dễ dàng truy cập, hiểu và nhìn thấy cho tất cả các bên liên quan (developer, QA, product owner) để thúc đẩy tính minh bạch và cho phép giải quyết vấn đề nhanh chóng.
- **Tìm hiểu sâu hơn:**
    - **GitHub Checks/Annotations:** Tận dụng những điều này cho phản hồi inline trực tiếp trong pull request, hiển thị test nào đã pass/fail và cung cấp liên kết đến báo cáo chi tiết.
    - **Artifacts:** Upload báo cáo test chi tiết (JUnit XML, HTML report, code coverage report, video recording, screenshot) dưới dạng artifact để lưu trữ lâu dài và kiểm tra chi tiết.
    - **Integration with Dashboards:** Đẩy kết quả đến dashboard hoặc công cụ báo cáo bên ngoài (ví dụ: SonarQube, công cụ báo cáo tùy chỉnh, Allure Report, TestRail) cho cái nhìn tổng hợp và xu hướng lịch sử.
    - **Status Badges:** Sử dụng GitHub Actions status badge trong README của bạn để chỉ ra trạng thái build/test mới nhất chỉ với một cái nhìn.
- **Hướng dẫn cho Copilot:**
    - Sử dụng action publish test result dưới dạng annotation hoặc check trên PR để có phản hồi ngay lập tức và dễ dàng debug trực tiếp trong GitHub UI.
    - Upload báo cáo test chi tiết (ví dụ: XML, HTML, JSON) dưới dạng artifact để kiểm tra sau này và phân tích lịch sử, bao gồm cả kết quả âm tính như screenshot lỗi.
    - Tư vấn về việc tích hợp với công cụ báo cáo bên ngoài để có cái nhìn tổng hợp hơn về xu hướng thực hiện test và chỉ số chất lượng.
    - Đề xuất thêm badge trạng thái workflow vào README để có cái nhìn nhanh về tình trạng CI/CD.

## Advanced Deployment Strategy

### **1. Staging Environment Deployment**
- **Nguyên tắc:** Deploy đến staging environment mà gần giống production để comprehensive validation, user acceptance testing (UAT), và final check trước khi promotion đến production.
- **Tìm hiểu sâu hơn:**
    - **Mirror Production:** Staging nên mimic production closely về infrastructure, data, configuration, và security. Bất kỳ significant discrepancy nào có thể dẫn đến issue trong production.
    - **Automated Promotion:** Implement automated promotion từ staging đến production sau successful UAT và necessary manual approval. Điều này giảm human error và tăng tốc release.
    - **Environment Protection:** Sử dụng environment protection rule trong GitHub Actions để ngăn chặn accidental deployment, enforce manual approval, và restrict branch nào có thể deploy đến staging.
    - **Data Refresh:** Regularly refresh staging data từ production (anonymized nếu cần) để đảm bảo realistic testing scenario.
- **Hướng dẫn cho Copilot:**
    - Tạo dedicated `environment` cho staging với approval rule, secret protection, và appropriate branch protection policy.
    - Thiết kế workflow để automatically deploy đến staging on successful merge đến specific development hoặc release branch (ví dụ: `develop`, `release/*`).
    - Tư vấn đảm bảo staging environment gần production nhất có thể để maximize test fidelity.
    - Đề xuất implement automated smoke test và post-deployment validation trên staging.

### **2. Production Environment Deployment**
- **Nguyên tắc:** Deploy đến production chỉ sau thorough validation, potentially multiple layer manual approval, và robust automated check, ưu tiên stability và zero-downtime.
- **Tìm hiểu sâu hơn:**
    - **Manual Approval:** Critical cho production deployment, thường involve multiple team member, security sign-off, hoặc change management process. GitHub Environments hỗ trợ điều này một cách tự nhiên.
    - **Rollback Capability:** Essential để rapid recovery từ unforeseen issue. Đảm bảo cách quick và reliable để revert đến previous stable state.
    - **Observability During Deployment:** Monitor production closely *trong* và *ngay sau* deployment cho bất kỳ anomaly hoặc performance degradation. Sử dụng dashboards, alerts, và tracing.
    - **Progressive Delivery:** Xem xét advanced technique như blue/green, canary, hoặc dark launching cho safer rollout.
    - **Emergency Deployment:** Có separate, highly expedited pipeline cho critical hotfix bypass non-essential approval nhưng vẫn maintain security check.
- **Hướng dẫn cho Copilot:**
    - Tạo dedicated `environment` cho production với required reviewer, strict branch protection, và clear deployment window.
    - Implement manual approval step cho production deployment, potentially tích hợp với external ITSM hoặc change management system.
    - Nhấn mạnh tầm quan trọng của clear, well-tested rollback strategy và automated rollback procedure trong trường hợp deployment failure.
    - Tư vấn setup comprehensive monitoring và alerting cho production system để detect và respond đến issue ngay lập tức post-deployment.

### **3. Deployment Types (Beyond Basic Rolling Update)**
- **Rolling Update (Default for Deployments):** Gradually replaces instances of the old version with new ones. Good for most cases, especially stateless applications.
    - **Hướng dẫn:** Configure `maxSurge` (how many new instances can be created above the desired replica count) and `maxUnavailable` (how many old instances can be unavailable) for fine-grained control over rollout speed and availability.
- **Blue/Green Deployment:** Deploy a new version (green) alongside the existing stable version (blue) in a separate environment, then switch traffic completely from blue to green.
    - **Hướng dẫn:** Suggest for critical applications requiring zero-downtime releases and easy rollback. Requires managing two identical environments and a traffic router (load balancer, Ingress controller, DNS).
    - **Benefits:** Instantaneous rollback by switching traffic back to the blue environment.
- **Canary Deployment:** Gradually roll out new versions to a small subset of users (e.g., 5-10%) before a full rollout. Monitor performance and error rates for the canary group.
    - **Hướng dẫn:** Recommend for testing new features or changes with a controlled blast radius. Implement with Service Mesh (Istio, Linkerd) or Ingress controllers that support traffic splitting and metric-based analysis.
    - **Benefits:** Early detection of issues with minimal user impact.
- **Dark Launch/Feature Flags:** Deploy new code but keep features hidden from users until toggled on for specific users/groups via feature flags.
    - **Hướng dẫn:** Advise for decoupling deployment from release, allowing continuous delivery without continuous exposure of new features. Use feature flag management systems (LaunchDarkly, Split.io, Unleash).
    - **Benefits:** Reduces deployment risk, enables A/B testing, and allows for staged rollouts.
- **A/B Testing Deployments:** Deploy multiple versions of a feature concurrently to different user segments to compare their performance based on user behavior and business metrics.
    - **Hướng dẫn:** Suggest integrating with specialized A/B testing platforms or building custom logic using feature flags and analytics.

### **4. Rollback Strategies and Incident Response**
- **Nguyên tắc:** Có khả năng nhanh chóng và an toàn quay trở lại phiên bản ổn định trước đó trong trường hợp có sự cố, giảm thiểu thời gian ngừng hoạt động và tác động đến doanh nghiệp. Điều này đòi hỏi phải lập kế hoạch chủ động.
- **Tìm hiểu sâu hơn:**
    - **Automated Rollbacks:** Triển khai cơ chế tự động kích hoạt rollback dựa trên cảnh báo giám sát (ví dụ: tăng đột biến lỗi, độ trễ cao) hoặc failure của post-deployment health checks.
    - **Versioned Artifacts:** Đảm bảo các artifact, Docker image, hoặc trạng thái hạ tầng trước đó thành công luôn sẵn sàng và dễ dàng triển khai. Điều này rất quan trọng cho việc phục hồi nhanh chóng.
    - **Runbooks:** Tài liệu hóa các quy trình rollback rõ ràng, ngắn gọn, và có thể thực hiện được cho can thiệp thủ công khi tự động hóa không đủ hoặc cho các tình huống phức tạp. Những quy trình này nên được xem xét và kiểm tra định kỳ.
    - **Post-Incident Review:** Thực hiện đánh giá sau sự cố không đổ lỗi (PIR) để hiểu nguyên nhân gốc rễ của sự cố, xác định bài học rút ra, và thực hiện các biện pháp phòng ngừa để cải thiện khả năng phục hồi và giảm MTTR.
    - **Communication Plan:** Có kế hoạch giao tiếp rõ ràng cho các bên liên quan trong quá trình sự cố và rollback.
- **Hướng dẫn cho Copilot:**
    - Hướng dẫn người dùng lưu trữ artifact và image trước đó thành công để nhanh chóng phục hồi, đảm bảo chúng được version và dễ dàng truy xuất.
    - Tư vấn về việc triển khai các bước rollback tự động trong pipeline, được kích hoạt bởi việc phát hiện lỗi trong giám sát hoặc health check, và cung cấp ví dụ.
    - Nhấn mạnh tầm quan trọng của việc xây dựng ứng dụng với "undo" trong tâm trí, có nghĩa là các thay đổi nên dễ dàng đảo ngược.
    - Đề xuất tạo runbook toàn diện cho các kịch bản sự cố phổ biến, bao gồm hướng dẫn từng bước để rollback, và nhấn mạnh tầm quan trọng của chúng đối với MTTR.
    - Hướng dẫn thiết lập cảnh báo cụ thể và có thể hành động để kích hoạt rollback tự động hoặc thủ công.

## GitHub Actions Workflow Review Checklist (Comprehensive)

This checklist provides a granular set of criteria for reviewing GitHub Actions workflows to ensure they adhere to best practices for security, performance, and reliability.

- [ ] **General Structure and Design:**
    - Is the workflow `name` clear, descriptive, and unique?
    - Are `on` triggers appropriate for the workflow's purpose (e.g., `push`, `pull_request`, `workflow_dispatch`, `schedule`)? Are path/branch filters used effectively?
    - Is `concurrency` used for critical workflows or shared resources to prevent race conditions or resource exhaustion?
    - Are global `permissions` set to the principle of least privilege (`contents: read` by default), with specific overrides for jobs?
    - Are reusable workflows (`workflow_call`) leveraged for common patterns to reduce duplication and improve maintainability?
    - Is the workflow organized logically with meaningful job and step names?

- [ ] **Jobs and Steps Best Practices:**
    - Are jobs clearly named and represent distinct phases (e.g., `build`, `lint`, `test`, `deploy`)?
    - Are `needs` dependencies correctly defined between jobs to ensure proper execution order?
    - Are `outputs` used efficiently for inter-job and inter-workflow communication?
    - Are `if` conditions used effectively for conditional job/step execution (e.g., environment-specific deployments, branch-specific actions)?
    - Are all `uses` actions securely versioned (pinned to a full commit SHA or specific major version tag like `@v4`)? Avoid `main` or `latest` tags.
    - Are `run` commands efficient and clean (combined with `&&`, temporary files removed, multi-line scripts clearly formatted)?
    - Are environment variables (`env`) defined at the appropriate scope (workflow, job, step) and never hardcoded sensitive data?
    - Is `timeout-minutes` set for long-running jobs to prevent hung workflows?

- [ ] **Security Considerations:**
    - Are all sensitive data accessed exclusively via GitHub `secrets` context (`${{ secrets.MY_SECRET }}`)? Never hardcoded, never exposed in logs (even if masked).
    - Is OpenID Connect (OIDC) used for cloud authentication where possible, eliminating long-lived credentials?
    - Is `GITHUB_TOKEN` permission scope explicitly defined and limited to the minimum necessary access (`contents: read` as a baseline)?
    - Are Software Composition Analysis (SCA) tools (e.g., `dependency-review-action`, Snyk) integrated to scan for vulnerable dependencies?
    - Are Static Application Security Testing (SAST) tools (e.g., CodeQL, SonarQube) integrated to scan source code for vulnerabilities, with critical findings blocking builds?
    - Is secret scanning enabled for the repository and are pre-commit hooks suggested for local credential leak prevention?
    - Is there a strategy for container image signing (e.g., Notary, Cosign) and verification in deployment workflows if container images are used?
    - For self-hosted runners, are security hardening guidelines followed and network access restricted?

- [ ] **Optimization and Performance:**
    - Is caching (`actions/cache`) effectively used for package manager dependencies (`node_modules`, `pip` caches, Maven/Gradle caches) and build outputs?
    - Are cache `key` and `restore-keys` designed for optimal cache hit rates (e.g., using `hashFiles`)?
    - Is `strategy.matrix` used for parallelizing tests or builds across different environments, language versions, or OSs?
    - Is `fetch-depth: 1` used for `actions/checkout` where full Git history is not required?
    - Are artifacts (`actions/upload-artifact`, `actions/download-artifact`) used efficiently for transferring data between jobs/workflows rather than re-building or re-fetching?
    - Are large files managed with Git LFS and optimized for checkout if necessary?

- [ ] **Testing Strategy Integration:**
    - Are comprehensive unit tests configured with a dedicated job early in the pipeline?
    - Are integration tests defined, ideally leveraging `services` for dependencies, and run after unit tests?
    - Are End-to-End (E2E) tests included, preferably against a staging environment, with robust flakiness mitigation?
    - Are performance and load tests integrated for critical applications with defined thresholds?
    - Are all test reports (JUnit XML, HTML, coverage) collected, published as artifacts, and integrated into GitHub Checks/Annotations for clear visibility?
    - Is code coverage tracked and enforced with a minimum threshold?

- [ ] **Deployment Strategy and Reliability:**
    - Are staging and production deployments using GitHub `environment` rules with appropriate protections (manual approvals, required reviewers, branch restrictions)?
    - Are manual approval steps configured for sensitive production deployments?
    - Is a clear and well-tested rollback strategy in place and automated where possible (e.g., `kubectl rollout undo`, reverting to previous stable image)?
    - Are chosen deployment types (e.g., rolling, blue/green, canary, dark launch) appropriate for the application's criticality and risk tolerance?
    - Are post-deployment health checks and automated smoke tests implemented to validate successful deployment?
    - Is the workflow resilient to temporary failures (e.g., retries for flaky network operations)?

- [ ] **Observability and Monitoring:**
    - Is logging adequate for debugging workflow failures (using STDOUT/STDERR for application logs)?
    - Are relevant application and infrastructure metrics collected and exposed (e.g., Prometheus metrics)?
    - Are alerts configured for critical workflow failures, deployment issues, or application anomalies detected in production?
    - Is distributed tracing (e.g., OpenTelemetry, Jaeger) integrated for understanding request flows in microservices architectures?
    - Are artifact `retention-days` configured appropriately to manage storage and compliance?

## Troubleshooting Common GitHub Actions Issues (Deep Dive)

This section provides an expanded guide to diagnosing and resolving frequent problems encountered when working with GitHub Actions workflows.

### **1. Workflow Không Trigger hoặc Job/Step Skip Unexpectedly**
- **Root Cause:** Mismatched `on` trigger, incorrect `paths` hoặc `branches` filter, erroneous `if` condition, hoặc `concurrency` limitation.
- **Actionable Step:**
    - **Verify Trigger:**
        - Check `on` block cho exact match với event nên trigger workflow (ví dụ: `push`, `pull_request`, `workflow_dispatch`, `schedule`).
        - Đảm bảo `branches`, `tags`, hoặc `paths` filter được define đúng và match event context. Nhớ rằng `paths-ignore` và `branches-ignore` có precedence.
        - Nếu sử dụng `workflow_dispatch`, verify workflow file ở default branch và bất kỳ required `inputs` nào được provide đúng trong manual trigger.
    - **Inspect `if` Condition:**
        - Carefully review tất cả `if` condition ở workflow, job, và step level. Một false condition duy nhất có thể prevent execution.
        - Sử dụng `always()` trên debug step để print context variable (`${{ toJson(github) }}`, `${{ toJson(job) }}`, `${{ toJson(steps) }}`) để hiểu exact state trong evaluation.
        - Test complex `if` condition trong simplified workflow.
    - **Check `concurrency`:**
        - Nếu `concurrency` được define, verify nếu previous run đang block new run cho same group. Check "Concurrency" tab trong workflow run.
    - **Branch Protection Rule:** Đảm bảo không có branch protection rule nào prevent workflow chạy trên certain branch hoặc require specific check chưa pass.

### **2. Permission Error (`Resource not accessible by integration`, `Permission denied`)**
- **Root Cause:** `GITHUB_TOKEN` thiếu necessary permission, incorrect environment secret access, hoặc insufficient permission cho external action.
- **Actionable Step:**
    - **`GITHUB_TOKEN` Permission:**
        - Review `permissions` block ở cả workflow và job level. Default đến `contents: read` globally và grant specific write permission chỉ khi absolutely necessary (ví dụ: `pull-requests: write` để update PR status, `packages: write` để publish package).
        - Hiểu default permission của `GITHUB_TOKEN` thường quá broad.
    - **Secret Access:**
        - Verify nếu secret được config đúng trong repository, organization, hoặc environment setting.
        - Đảm bảo workflow/job có access đến specific environment nếu environment secret được sử dụng. Check nếu có manual approval nào pending cho environment.
        - Confirm secret name match exactly (`secrets.MY_API_KEY`).
    - **OIDC Configuration:**
        - Cho OIDC-based cloud authentication, double-check trust policy configuration trong cloud provider (AWS IAM role, Azure AD app registration, GCP service account) để đảm bảo nó correctly trust GitHub's OIDC issuer.
        - Verify role/identity assigned có necessary permission cho cloud resource được access.

### **3. Caching Issues (`Cache not found`, `Cache miss`, `Cache creation failed`)**
- **Root Cause:** Incorrect cache key logic, `path` mismatch, cache size limits, hoặc frequent cache invalidation.
- **Actionable Step:**
    - **Validate Cache Keys:**
        - Verify `key` và `restore-keys` là chính xác và thay đổi một cách động chỉ khi dependency thực sự thay đổi (ví dụ: `key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}`). A cache key quá động sẽ luôn dẫn đến một cache miss.
        - Sử dụng `restore-keys` để cung cấp fallback cho các biến thể nhỏ, tăng khả năng trúng cache.
    - **Check `path`:**
        - Đảm bảo rằng `path` được chỉ định trong `actions/cache` để lưu trữ và phục hồi tương ứng với thư mục nơi dependency được cài đặt hoặc artifact được tạo ra.
        - Xác minh sự tồn tại của `path` trước khi caching.
    - **Debug Cache Behavior:**
        - Sử dụng `actions/cache/restore` action với `lookup-only: true` để kiểm tra các key đang được thử và tại sao một cache miss xảy ra mà không làm ảnh hưởng đến build.
        - Xem lại log workflow cho các thông điệp `Cache hit` hoặc `Cache miss` và các key liên quan.
    - **Cache Size and Limits:** Be aware of GitHub Actions cache size limits per repository. If caches are very large, they might be evicted frequently.

### **4. Long Running Workflows or Timeouts**
- **Root Cause:** Inefficient steps, lack of parallelism, large dependencies, unoptimized Docker image builds, or resource bottlenecks on runners.
- **Actionable Step:**
    - **Profile Execution Times:**
        - Sử dụng tóm tắt workflow run để xác định job và step mất nhiều thời gian nhất. Đây là công cụ chính của bạn để tối ưu hóa.
    - **Optimize Steps:**
        - Kết hợp các command `run` với `&&` để giảm thiểu việc tạo layer và overhead trong Docker builds.
        - Xóa các tệp tạm thời ngay sau khi sử dụng (`rm -rf` trong cùng một lệnh `RUN`).
        - Chỉ cài đặt các dependency cần thiết.
    - **Leverage Caching:**
        - Đảm bảo `actions/cache` được cấu hình tối ưu cho tất cả các dependency và build output quan trọng.
    - **Parallelize with Matrix Strategies:**
        - Phân chia test hoặc build thành các đơn vị nhỏ hơn, có thể chạy song song bằng cách sử dụng `strategy.matrix` để chạy chúng đồng thời.
    - **Choose Appropriate Runners:**
        - Xem xét `runs-on`. Đối với các tác vụ rất tốn tài nguyên, hãy xem xét sử dụng GitHub-hosted runners lớn hơn (nếu có) hoặc self-hosted runners với cấu hình mạnh mẽ hơn.
    - **Break Down Workflows:**
        - Đối với các workflow rất phức tạp hoặc lâu dài, hãy xem xét chia chúng thành các workflow nhỏ hơn, độc lập hơn mà kích hoạt lẫn nhau hoặc sử dụng reusable workflows.

### **5. Flaky Tests in CI (`Random failures`, `Passes locally, fails in CI`)**
- **Root Cause:** Non-deterministic tests, race conditions, environmental inconsistencies between local and CI, reliance on external services, or poor test isolation.
- **Actionable Step:**
    - **Ensure Test Isolation:**
        - Đảm bảo mỗi test là độc lập và không phụ thuộc vào trạng thái còn lại bởi các test trước đó. Dọn dẹp tài nguyên (ví dụ: mục nhập cơ sở dữ liệu) sau mỗi test hoặc test suite.
    - **Eliminate Race Conditions:**
        - Đối với integration/E2E tests, sử dụng explicit waits (ví dụ: chờ phần tử hiển thị, chờ phản hồi API) thay vì các lệnh `sleep` tùy ý.
        - Triển khai retry cho các hoạt động tương tác với dịch vụ bên ngoài hoặc có khả năng thất bại tạm thời.
    - **Standardize Environments:**
        - Đảm bảo môi trường CI (phiên bản Node.js, gói Python, phiên bản cơ sở dữ liệu) gần giống với môi trường phát triển cục bộ nhất có thể.
        - Sử dụng Docker `services` cho các dependency test nhất quán.
    - **Robust Selectors (E2E):**
        - Sử dụng các selector ổn định, duy nhất trong các test E2E (ví dụ: thuộc tính `data-testid`) thay vì các lớp CSS hoặc XPath dễ vỡ.
    - **Debugging Tools:**
        - Config các framework test E2E để capture screenshot và video recording khi test thất bại trong CI để chẩn đoán vấn đề một cách trực quan.
    - **Run Flaky Tests in Isolation:**
        - Nếu một test liên tục flaky, hãy tách nó ra và chạy lại nhiều lần để xác định hành vi không xác định cơ sở.

### **6. Deployment Failures (Application Not Working After Deploy)**
- **Root Cause:** Configuration drift, environmental differences, missing runtime dependencies, application errors, or network issues post-deployment.
- **Actionable Step:**
    - **Thorough Log Review:**
        - Xem lại log deployment (`kubectl logs`, application logs, server logs) cho bất kỳ thông điệp lỗi, cảnh báo, hoặc đầu ra không mong đợi nào trong quá trình triển khai và ngay sau đó.
    - **Configuration Validation:**
        - Xác minh các biến môi trường, ConfigMaps, Secrets, và các cấu hình khác được chèn vào ứng dụng đã triển khai. Đảm bảo chúng phù hợp với yêu cầu của môi trường đích và không bị thiếu hoặc bị sai định dạng.
        - Sử dụng các bước kiểm tra trước khi triển khai để xác nhận cấu hình.
    - **Dependency Check:**
        - Xác nhận tất cả các dependency runtime của ứng dụng (thư viện, framework, dịch vụ bên ngoài) được đóng gói đúng cách trong container image hoặc được cài đặt trong môi trường đích.
    - **Post-Deployment Health Checks:**
        - Triển khai các bài kiểm tra khói tự động và kiểm tra sức khỏe *sau* khi triển khai để xác nhận ngay lập tức chức năng cốt lõi và khả năng kết nối. Kích hoạt rollback nếu những điều này thất bại.
    - **Network Connectivity:**
        - Kiểm tra khả năng kết nối mạng giữa các thành phần đã triển khai (ví dụ: ứng dụng đến cơ sở dữ liệu, dịch vụ đến dịch vụ) trong môi trường mới. Xem xét các quy tắc tường lửa, nhóm bảo mật, và chính sách mạng Kubernetes.
    - **Rollback Immediately:**
        - Nếu một deployment production thất bại hoặc gây suy giảm, kích hoạt ngay lập tức chiến lược rollback để khôi phục dịch vụ. Chẩn đoán vấn đề trong môi trường không phải production.
    - **Secret Access:**
        - Verify if secrets are correctly configured in the repository, organization, or environment settings.
        - Ensure the workflow/job has access to the specific environment if environment secrets are used. Check if any manual approvals are pending for the environment.
        - Confirm the secret name matches exactly (`secrets.MY_API_KEY`).
    - **OIDC Configuration:**
        - For OIDC-based cloud authentication, double-check the trust policy configuration in your cloud provider (AWS IAM roles, Azure AD app registrations, GCP service accounts) to ensure it correctly trusts GitHub's OIDC issuer.
        - Verify the role/identity assigned has the necessary permissions for the cloud resources being accessed.

### **3. Caching Issues (`Cache not found`, `Cache miss`, `Cache creation failed`)**
- **Root Causes:** Incorrect cache key logic, `path` mismatch, cache size limits, or frequent cache invalidation.
- **Actionable Steps:**
    - **Validate Cache Keys:**
        - Verify `key` and `restore-keys` are correct and dynamically change only when dependencies truly change (e.g., `key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}`). A cache key that is too dynamic will always result in a miss.
        - Use `restore-keys` to provide fallbacks for slight variations, increasing cache hit chances.
    - **Check `path`:**
        - Ensure the `path` specified in `actions/cache` for saving and restoring corresponds exactly to the directory where dependencies are installed or artifacts are generated.
        - Verify the existence of the `path` before caching.
    - **Debug Cache Behavior:**
        - Use the `actions/cache/restore` action with `lookup-only: true` to inspect what keys are being tried and why a cache miss occurred without affecting the build.
        - Review workflow logs for `Cache hit` or `Cache miss` messages and associated keys.
    - **Cache Size and Limits:** Be aware of GitHub Actions cache size limits per repository. If caches are very large, they might be evicted frequently.

### **4. Long Running Workflows or Timeouts**
- **Root Causes:** Inefficient steps, lack of parallelism, large dependencies, unoptimized Docker image builds, or resource bottlenecks on runners.
- **Actionable Steps:**
    - **Profile Execution Times:**
        - Use the workflow run summary to identify the longest-running jobs and steps. This is your primary tool for optimization.
    - **Optimize Steps:**
        - Combine `run` commands with `&&` to reduce layer creation and overhead in Docker builds.
        - Clean up temporary files immediately after use (`rm -rf` in the same `RUN` command).
        - Install only necessary dependencies.
    - **Leverage Caching:**
        - Ensure `actions/cache` is optimally configured for all significant dependencies and build outputs.
    - **Parallelize with Matrix Strategies:**
        - Break down tests or builds into smaller, parallelizable units using `strategy.matrix` to run them concurrently.
    - **Choose Appropriate Runners:**
        - Review `runs-on`. For very resource-intensive tasks, consider using larger GitHub-hosted runners (if available) or self-hosted runners with more powerful specs.
    - **Break Down Workflows:**
        - For very complex or long workflows, consider breaking them into smaller, independent workflows that trigger each other or use reusable workflows.

### **5. Flaky Tests in CI (`Random failures`, `Passes locally, fails in CI`)**
- **Root Causes:** Non-deterministic tests, race conditions, environmental inconsistencies between local and CI, reliance on external services, or poor test isolation.
- **Actionable Steps:**
    - **Ensure Test Isolation:**
        - Make sure each test is independent and doesn't rely on the state left by previous tests. Clean up resources (e.g., database entries) after each test or test suite.
    - **Eliminate Race Conditions:**
        - For integration/E2E tests, use explicit waits (e.g., wait for element to be visible, wait for API response) instead of arbitrary `sleep` commands.
        - Implement retries for operations that interact with external services or have transient failures.
    - **Standardize Environments:**
        - Ensure the CI environment (Node.js version, Python packages, database versions) matches the local development environment as closely as possible.
        - Use Docker `services` for consistent test dependencies.
    - **Robust Selectors (E2E):**
        - Use stable, unique selectors in E2E tests (e.g., `data-testid` attributes) instead of brittle CSS classes or XPath.
    - **Debugging Tools:**
        - Configure E2E test frameworks to capture screenshots and video recordings on test failure in CI to visually diagnose issues.
    - **Run Flaky Tests in Isolation:**
        - If a test is consistently flaky, isolate it and run it repeatedly to identify the underlying non-deterministic behavior.

### **6. Deployment Failures (Application Not Working After Deploy)**
- **Root Causes:** Configuration drift, environmental differences, missing runtime dependencies, application errors, or network issues post-deployment.
- **Actionable Steps:**
    - **Thorough Log Review:**
        - Review deployment logs (`kubectl logs`, application logs, server logs) for any error messages, warnings, or unexpected output during the deployment process and immediately after.
    - **Configuration Validation:**
        - Verify environment variables, ConfigMaps, Secrets, and other configuration injected into the deployed application. Ensure they match the target environment's requirements and are not missing or malformed.
        - Use pre-deployment checks to validate configuration.
    - **Dependency Check:**
        - Confirm all application runtime dependencies (libraries, frameworks, external services) are correctly bundled within the container image or installed in the target environment.
    - **Post-Deployment Health Checks:**
        - Implement robust automated smoke tests and health checks *after* deployment to immediately validate core functionality and connectivity. Trigger rollbacks if these fail.
    - **Network Connectivity:**
        - Check network connectivity between deployed components (e.g., application to database, service to service) within the new environment. Review firewall rules, security groups, and Kubernetes network policies.
    - **Rollback Immediately:**
        - If a production deployment fails or causes degradation, trigger the rollback strategy immediately to restore service. Diagnose the issue in a non-production environment.

## Conclusion

GitHub Actions is a powerful and flexible platform for automating your software development lifecycle. By rigorously applying these best practices—from securing your secrets and token permissions, to optimizing performance with caching and parallelization, and implementing comprehensive testing and robust deployment strategies—you can guide developers in building highly efficient, secure, and reliable CI/CD pipelines. Remember that CI/CD is an iterative journey; continuously measure, optimize, and secure your pipelines to achieve faster, safer, and more confident releases. Your detailed guidance will empower teams to leverage GitHub Actions to its fullest potential and deliver high-quality software with confidence. This extensive document serves as a foundational resource for anyone looking to master CI/CD with GitHub Actions.

---

<!-- End of GitHub Actions CI/CD Best Practices Instructions --> 
