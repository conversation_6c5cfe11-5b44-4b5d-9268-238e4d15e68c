---
description: 'Tiêu chuẩn phát triển ReactJS và best practices'
applyTo: '**/*.jsx, **/*.tsx, **/*.js, **/*.ts, **/*.css, **/*.scss'
type: "agent_requested"
---

# Hướng dẫn phát triển ReactJS

Hướng dẫn xây dựng ứng dụng ReactJS chất lượng cao với các patterns hiện đại, hooks, và best practices theo tài liệu chính thức React tại https://react.dev.

## Bối cảnh dự án
- React phiên bản mới nhất (React 19+)
- TypeScript cho type safety (khi áp dụng)
- Functional components với hooks làm mặc định
- Tuân theo style guide chính thức và best practices của React
- Sử dụng modern build tools (Vite, Create React App, hoặc custom Webpack setup)
- Implement component composition và reusability patterns phù hợp

## Tiêu chuẩn phát triển

### Architecture
- Sử dụng functional components với hooks làm pattern chính
- Implement component composition thay vì inheritance
- Tổ chức components theo feature hoặc domain để scalability
- Phân tách rõ ràng presentational và container components
- Sử dụng custom hooks cho reusable stateful logic
- Implement component hierarchies phù hợp với data flow rõ ràng

### TypeScript Integration
- Sử dụng TypeScript interfaces cho props, state, và component definitions
- Định nghĩa types phù hợp cho event handlers và refs
- Implement generic components khi thích hợp
- Sử dụng strict mode trong `tsconfig.json` cho type safety
- Tận dụng built-in types của React (`React.FC`, `React.ComponentProps`, v.v.)
- Tạo union types cho component variants và states

### Component Design
- Tuân theo single responsibility principle cho components
- Sử dụng naming conventions mô tả và nhất quán
- Implement prop validation phù hợp với TypeScript hoặc PropTypes
- Thiết kế components để testable và reusable
- Giữ components nhỏ và tập trung vào single concern
- Sử dụng composition patterns (render props, children as functions)

### State Management
- Sử dụng `useState` cho local component state
- Implement `useReducer` cho complex state logic
- Tận dụng `useContext` để chia sẻ state across component trees
- Cân nhắc external state management (Redux Toolkit, Zustand) cho complex applications
- Implement state normalization và data structures phù hợp
- Sử dụng React Query hoặc SWR cho server state management

### Hooks và Effects
- Sử dụng `useEffect` với dependency arrays phù hợp để tránh infinite loops
- Implement cleanup functions trong effects để tránh memory leaks
- Sử dụng `useMemo` và `useCallback` cho performance optimization khi cần
- Tạo custom hooks cho reusable stateful logic
- Tuân theo rules of hooks (chỉ gọi ở top level)
- Sử dụng `useRef` để truy cập DOM elements và lưu trữ mutable values

### Styling
- Sử dụng CSS Modules, Styled Components, hoặc modern CSS-in-JS solutions
- Implement responsive design với mobile-first approach
- Tuân theo BEM methodology hoặc naming conventions tương tự cho CSS classes
- Sử dụng CSS custom properties (variables) cho theming
- Implement spacing, typography, và color systems nhất quán
- Đảm bảo accessibility với ARIA attributes và semantic HTML phù hợp

### Performance Optimization
- Sử dụng `React.memo` cho component memoization khi thích hợp
- Implement code splitting với `React.lazy` và `Suspense`
- Tối ưu bundle size với tree shaking và dynamic imports
- Sử dụng `useMemo` và `useCallback` một cách thận trọng để tránh unnecessary re-renders
- Implement virtual scrolling cho large lists
- Profile components với React DevTools để xác định performance bottlenecks

### Data Fetching
- Sử dụng modern data fetching libraries (React Query, SWR, Apollo Client)
- Implement loading, error, và success states phù hợp
- Xử lý race conditions và request cancellation
- Sử dụng optimistic updates cho better user experience
- Implement caching strategies phù hợp
- Xử lý offline scenarios và network errors một cách graceful

### Error Handling
- Implement Error Boundaries cho component-level error handling
- Sử dụng error states phù hợp trong data fetching
- Implement fallback UI cho error scenarios
- Log errors phù hợp cho debugging
- Xử lý async errors trong effects và event handlers
- Cung cấp error messages có ý nghĩa cho users

### Forms và Validation
- Sử dụng controlled components cho form inputs
- Implement form validation phù hợp với libraries như Formik, React Hook Form
- Xử lý form submission và error states phù hợp
- Implement accessibility features cho forms (labels, ARIA attributes)
- Sử dụng debounced validation cho better user experience
- Xử lý file uploads và complex form scenarios

### Routing
- Sử dụng React Router cho client-side routing
- Implement nested routes và route protection
- Xử lý route parameters và query strings phù hợp
- Implement lazy loading cho route-based code splitting
- Sử dụng navigation patterns và back button handling phù hợp
- Implement breadcrumbs và navigation state management

### Testing
- Viết unit tests cho components sử dụng React Testing Library
- Test component behavior, không phải implementation details
- Sử dụng Jest cho test runner và assertion library
- Implement integration tests cho complex component interactions
- Mock external dependencies và API calls phù hợp
- Test accessibility features và keyboard navigation

### Security
- Sanitize user inputs để tránh XSS attacks
- Validate và escape data trước khi rendering
- Sử dụng HTTPS cho tất cả external API calls
- Implement authentication và authorization patterns phù hợp
- Tránh lưu trữ sensitive data trong localStorage hoặc sessionStorage
- Sử dụng Content Security Policy (CSP) headers

### Accessibility
- Sử dụng semantic HTML elements phù hợp
- Implement ARIA attributes và roles phù hợp
- Đảm bảo keyboard navigation hoạt động cho tất cả interactive elements
- Cung cấp alt text cho images và descriptive text cho icons
- Implement color contrast ratios phù hợp
- Test với screen readers và accessibility tools

## Quy trình Implementation
1. Lập kế hoạch component architecture và data flow
2. Thiết lập project structure với folder organization phù hợp
3. Định nghĩa TypeScript interfaces và types
4. Implement core components với styling phù hợp
5. Thêm state management và data fetching logic
6. Implement routing và navigation
7. Thêm form handling và validation
8. Implement error handling và loading states
9. Thêm testing coverage cho components và functionality
10. Tối ưu performance và bundle size
11. Đảm bảo accessibility compliance
12. Thêm documentation và code comments

## Hướng dẫn bổ sung
- Tuân theo naming conventions của React (PascalCase cho components, camelCase cho functions)
- Sử dụng commit messages có ý nghĩa và duy trì clean git history
- Implement code splitting và lazy loading strategies phù hợp
- Document complex components và custom hooks với JSDoc
- Sử dụng ESLint và Prettier cho consistent code formatting
- Giữ dependencies up to date và audit cho security vulnerabilities
- Implement environment configuration phù hợp cho các deployment stages khác nhau
- Sử dụng React Developer Tools cho debugging và performance analysis

## Patterns phổ biến
- Higher-Order Components (HOCs) cho cross-cutting concerns
- Render props pattern cho component composition
- Compound components cho related functionality
- Provider pattern cho context-based state sharing
- Container/Presentational component separation
- Custom hooks cho reusable logic extraction
