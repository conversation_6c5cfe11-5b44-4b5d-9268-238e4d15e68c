---
description: 'Hướng dẫn xây dựng ứng dụng TanStack Start'
applyTo: '**/*.ts, **/*.tsx, **/*.js, **/*.jsx, **/*.css, **/*.scss, **/*.json'
type: "agent_requested"
---

# Hướng dẫn Phát triển TanStack Start với Shadcn/ui

## Tech Stack
- TypeScript (strict mode)
- TanStack Start (routing & SSR)
- Shadcn/ui (UI component)
- Tailwind CSS (styling)
- Zod (validation)
- TanStack Query (client state)

## Quy tắc Code Style

- KHÔNG BAO GIỜ sử dụng type `any` - luôn sử dụng TypeScript type chính xác
- Ưu tiên function component hơn class component
- Luôn validate external data với Zod schema
- <PERSON><PERSON> gồm error và pending boundary cho tất cả route
- Tuân theo accessibility best practice với ARIA attribute

## Component Pattern

Sử dụng function component với TypeScript interface phù hợp:

```typescript
interface ButtonProps {
  children: React.ReactNode;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

export default function Button({ children, onClick, variant = 'primary' }: ButtonProps) {
  return (
    <button onClick={onClick} className={cn(buttonVariants({ variant }))}>
      {children}
    </button>
  );
}
```

## Data Fetching

Sử dụng Route Loader cho:
- Initial page data cần thiết để render
- Yêu cầu SSR
- Data quan trọng cho SEO

Sử dụng React Query cho:
- Data cập nhật thường xuyên
- Data tùy chọn/phụ
- Client mutation với optimistic update

```typescript
// Route Loader
export const Route = createFileRoute('/users')({
  loader: async () => {
    const users = await fetchUsers()
    return { users: userListSchema.parse(users) }
  },
  component: UserList,
})

// React Query
const { data: stats } = useQuery({
  queryKey: ['user-stats', userId],
  queryFn: () => fetchUserStats(userId),
  refetchInterval: 30000,
});
```

## Zod Validation

Luôn validate external data. Định nghĩa schema trong `src/lib/schemas.ts`:

```typescript
export const userSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(100),
  email: z.string().email().optional(),
  role: z.enum(['admin', 'user']).default('user'),
})

export type User = z.infer<typeof userSchema>

// Safe parsing
const result = userSchema.safeParse(data)
if (!result.success) {
  console.error('Validation failed:', result.error.format())
  return null
}
```

## Route

Cấu trúc route trong `src/routes/` với file-based routing. Luôn bao gồm error và pending boundary:

```typescript
export const Route = createFileRoute('/users/$id')({
  loader: async ({ params }) => {
    const user = await fetchUser(params.id);
    return { user: userSchema.parse(user) };
  },
  component: UserDetail,
  errorBoundary: ({ error }) => (
    <div className="text-red-600 p-4">Error: {error.message}</div>
  ),
  pendingBoundary: () => (
    <div className="flex items-center justify-center p-4">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
    </div>
  ),
});
```

## UI Component

Luôn ưu tiên Shadcn/ui component hơn custom component:

```typescript
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

<Card>
  <CardHeader>
    <CardTitle>User Details</CardTitle>
  </CardHeader>
  <CardContent>
    <Button onClick={handleSave}>Save</Button>
  </CardContent>
</Card>
```

Sử dụng Tailwind cho styling với responsive design:

```typescript
<div className="flex flex-col gap-4 p-6 md:flex-row md:gap-6">
  <Button className="w-full md:w-auto">Action</Button>
</div>
```

## Accessibility

Sử dụng semantic HTML trước. Chỉ thêm ARIA khi không có semantic tương đương:

```typescript
// ✅ Tốt: Semantic HTML với ARIA tối thiểu
<button onClick={toggleMenu}>
  <MenuIcon aria-hidden="true" />
  <span className="sr-only">Toggle Menu</span>
</button>

// ✅ Tốt: ARIA chỉ khi cần thiết (cho dynamic state)
<button
  aria-expanded={isOpen}
  aria-controls="menu"
  onClick={toggleMenu}
>
  Menu
</button>

// ✅ Tốt: Semantic form element
<label htmlFor="email">Email Address</label>
<input id="email" type="email" />
{errors.email && (
  <p role="alert">{errors.email}</p>
)}
```

## File Organization

```
src/
├── components/ui/    # Shadcn/ui component
├── lib/schemas.ts    # Zod schema
├── routes/          # File-based route
└── routes/api/      # Server route (.ts)
```

## Import Standard

Sử dụng alias `@/` cho tất cả internal import:

```typescript
// ✅ Tốt
import { Button } from '@/components/ui/button'
import { userSchema } from '@/lib/schemas'

// ❌ Không tốt
import { Button } from '../components/ui/button'
```

## Thêm Component

Cài đặt Shadcn component khi cần:

```bash
npx shadcn@latest add button card input dialog
```

## Pattern phổ biến

- Luôn validate external data với Zod
- Sử dụng route loader cho initial data, React Query cho update
- Bao gồm error/pending boundary trên tất cả route
- Ưu tiên Shadcn component hơn custom UI
- Sử dụng import `@/` một cách nhất quán
- Tuân theo accessibility best practice
