---
description: 'Hướng dẫn xây dựng ứng dụng Java cơ bản'
applyTo: '**/*.java'
type: "agent_requested"
---

# Java Development

## Hướng dẫn chung

- <PERSON><PERSON><PERSON> tiên, hỏi user xem họ có muốn tích hợp static analysis tool (SonarQube, PMD, Checkstyle)
  vào project setup hay không. Nếu có, cung cấp guidance về tool selection và configuration.
- Nếu user từ chối static analysis tool hoặc muốn tiếp tục mà không có chúng, tiếp tục implement Best practice, bug pattern và code smell prevention guideline được nêu dưới đây.
- Address code smell một cách proactive trong quá trình development thay vì tích lũy technical debt.
- Tập trung vào readability, maintainability, và performance khi refactor những issue đã xác định.
- Sử dụng IDE / Code editor reported warning và suggestion để catch common pattern sớm trong development.

## Best Practice

- **Record**: Đối với class chủ yếu dùng để store data (ví dụ: DTO, immutable data structure), **Java Record nên được sử dụng thay vì traditional class**.
- **Pattern Matching**: Sử dụng pattern matching cho `instanceof` và `switch` expression để đơn giản hóa conditional logic và type casting.
- **Type Inference**: Sử dụng `var` cho local variable declaration để cải thiện readability, nhưng chỉ khi type rõ ràng từ right-hand side của expression.
- **Immutability**: Ưu tiên immutable object. Tạo class và field `final` khi có thể. Sử dụng collection từ `List.of()`/`Map.of()` cho fixed data. Sử dụng `Stream.toList()` để tạo immutable list.
- **Stream và Lambda**: Sử dụng Stream API và lambda expression cho collection processing. Áp dụng method reference (ví dụ: `stream.map(Foo::toBar)`).
- **Null Handling**: Tránh return hoặc accept `null`. Sử dụng `Optional<T>` cho possibly-absent value và `Objects` utility method như `equals()` và `requireNonNull()`.

### Naming Convention

- Tuân theo Google's Java style guide:
  - `UpperCamelCase` cho class và interface name.
  - `lowerCamelCase` cho method và variable name.
  - `UPPER_SNAKE_CASE` cho constant.
  - `lowercase` cho package name.
- Sử dụng noun cho class (`UserService`) và verb cho method (`getUserById`).
- Tránh abbreviation và Hungarian notation.

### Bug Pattern

| Rule ID | Mô tả                                                      | Example / Note                                                                                   |
| ------- | ---------------------------------------------------------- | ------------------------------------------------------------------------------------------------ |
| `S2095` | Resource nên được đóng                                     | Sử dụng try-with-resources khi làm việc với stream, file, socket, v.v.                           |
| `S1698` | Object nên được so sánh với `.equals()` thay vì `==`      | Đặc biệt quan trọng cho String và boxed primitive.                                               |
| `S1905` | Redundant cast nên được loại bỏ                           | Clean up unnecessary hoặc unsafe cast.                                                           |
| `S3518` | Condition không nên luôn evaluate thành true hoặc false   | Chú ý infinite loop hoặc if-condition không bao giờ thay đổi.                                   |
| `S108`  | Unreachable code nên được loại bỏ                         | Code sau `return`, `throw`, v.v., phải được clean up.                                           |

## Code Smell

| Rule ID | Mô tả                                                      | Example / Note                                                               |
| ------- | ---------------------------------------------------------- | ----------------------------------------------------------------------------- |
| `S107`  | Method không nên có quá nhiều parameter                   | Refactor thành helper class hoặc sử dụng builder pattern.                    |
| `S121`  | Duplicated block code nên được loại bỏ                    | Consolidate logic thành shared method.                                       |
| `S138`  | Method không nên quá dài                                  | Chia complex logic thành smaller, testable unit.                             |
| `S3776` | Cognitive complexity nên được giảm                        | Đơn giản hóa nested logic, extract method, tránh deep `if` tree.             |
| `S1192` | String literal không nên được duplicate                   | Thay thế bằng constant hoặc enum.                                            |
| `S1854` | Unused assignment nên được loại bỏ                        | Tránh dead variable—loại bỏ hoặc refactor.                                    |
| `S109`  | Magic number nên được thay thế bằng constant              | Cải thiện readability và maintainability.                                    |
| `S1188` | Catch block không nên empty                               | Luôn log hoặc handle exception một cách meaningful.                          |

## Build và Verification

- Sau khi thêm hoặc modify code, verify project tiếp tục build thành công.
- Nếu project sử dụng Maven, chạy `mvn clean install`.
- Nếu project sử dụng Gradle, chạy `./gradlew build` (hoặc `gradlew.bat build` trên Windows).
- Đảm bảo tất cả test pass như một phần của build.
