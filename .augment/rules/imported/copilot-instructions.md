---
type: "always_apply"
---

### **<PERSON><PERSON><PERSON><PERSON> tắc chung**

1.  **Ngôn ngữ:**
    * <PERSON><PERSON><PERSON> hồi bằng **tiếng Việt**.
    * Giữ nguyên thuật ngữ gốc **tiếng Anh** cho chuyên ngành Công nghệ thông tin và Phát triển phần mềm.

2.  **Phạm vi công việc:**
    * Chỉ thực hiện và lập tài liệu kiểm thử (testing) khi có yêu cầu cụ thể.

3.  **Lưu trữ tài liệu:**
    * Tất cả tài liệu phải được lưu dưới dạng **Markdown**.
    * Thư mục gốc cho tài liệu là `/docs`.

### **Quy trình xử lý**

#### **1. <PERSON>u<PERSON>n lý Hướng dẫn (Guidelines)**

* **Lưu trữ:** <PERSON><PERSON>u tài liệu hướng dẫn chi tiết trong thư mục `/docs/guidelines/`.
* **Tóm tắt:** C<PERSON><PERSON> nhật file `/docs/guidelines/summary.md` với các mục: `tên hướng dẫn`, `mô tả tóm tắt`, và `tag`.

#### **2. Xử lý Lỗi (Issues)**

Khi nhận được yêu cầu sửa lỗi, hãy thực hiện theo các bước sau:

1.  **Tra cứu:** Tìm kiếm trong file `/docs/issues/summary.md` bằng `tag` để xác định các lỗi tương tự đã được giải quyết.
2.  **Tham khảo:** Nếu tìm thấy lỗi tương tự, hãy sử dụng giải pháp chi tiết của lỗi đó làm tài liệu tham khảo.
3.  **Lập tài liệu giải pháp:**
    * Tạo một file Markdown mới trong `/docs/issues/` để ghi lại giải pháp cho lỗi hiện tại.
    * Nội dung bao gồm: **Phân tích lỗi**, **Giải pháp**, và **Kết quả**.
4.  **Cập nhật tóm tắt:** Thêm một mục mới vào file `/docs/issues/summary.md` bao gồm: `tên lỗi`, `mô tả tóm tắt`, và `tag`.