---
applyTo: '**/*.yaml, **/*.yml'
description: 'Hướng dẫn toàn diện về các thực hành tốt nhất để triển khai và quản lý ứng dụng trên Kubernetes. Ba<PERSON> gồm Pods, Deployments, Services, Ingress, ConfigMaps, Secrets, health checks, resource limits, scaling và security contexts.'
type: "agent_requested"
---

# Thực Hành Tốt Nhất Cho Kubernetes Deployment

## Các Khái Niệm Cốt Lõi Của Kubernetes Cho Deployment

### **1. Pods**
- **Nguyên tắc:** Đơn vị triển khai nhỏ nhất trong Kubernetes. Đại diện cho một instance duy nhất của một process đang chạy trong cluster của bạn.
- **Hướng dẫn cho Copilot:**
    - Thiế<PERSON> kế Pods để chạy một container chính duy nhất (hoặc các sidecar được kết nối chặt chẽ).
    - <PERSON><PERSON><PERSON> nghĩa `resources` (requests/limits) cho CPU và memory để ngăn chặn tình trạng cạn kiệt tài nguyên.
    - Triển khai `livenessProbe` và `readinessProbe` cho health checks.
- **Mẹo:** Tránh triển khai Pods trực tiếp; sử dụng các controller cấp cao hơn như Deployments hoặc StatefulSets.

### **2. Deployments**
- **Nguyên tắc:** Quản lý một tập hợp các Pods giống hệt nhau và đảm bảo chúng đang chạy. Xử lý rolling updates và rollbacks.
- **Hướng dẫn cho Copilot:**
    - Sử dụng Deployments cho các ứng dụng stateless.
    - Định nghĩa số replicas mong muốn (`replicas`).
    - Chỉ định `selector` và `template` để matching Pods.
    - Cấu hình `strategy` cho rolling updates (`rollingUpdate` với `maxSurge`/`maxUnavailable`).
- **Ví dụ (Deployment đơn giản):**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-app-deployment
  labels:
    app: my-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: my-app
  template:
    metadata:
      labels:
        app: my-app
    spec:
      containers:
        - name: my-app-container
          image: my-repo/my-app:1.0.0
          ports:
            - containerPort: 8080
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
          livenessProbe:
            httpGet:
              path: /healthz
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 20
          readinessProbe:
            httpGet:
              path: /readyz
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
```

### **3. Services**
- **Nguyên tắc:** Một cách trừu tượng để expose một ứng dụng đang chạy trên một tập hợp Pods như một network service.
- **Hướng dẫn cho Copilot:**
    - Sử dụng Services để cung cấp network identity ổn định cho Pods.
    - Chọn `type` dựa trên nhu cầu expose (ClusterIP, NodePort, LoadBalancer, ExternalName).
    - Đảm bảo `selector` khớp với Pod labels để routing đúng cách.
- **Mẹo:** Sử dụng `ClusterIP` cho internal services, `LoadBalancer` cho các ứng dụng hướng internet trong môi trường cloud.

### **4. Ingress**
- **Nguyên tắc:** Quản lý truy cập external đến services trong cluster, thường là các HTTP/HTTPS routes từ bên ngoài cluster đến các services bên trong.
- **Hướng dẫn cho Copilot:**
    - Sử dụng Ingress để consolidate routing rules và quản lý TLS termination.
    - Cấu hình Ingress resources cho external access khi sử dụng web application.
    - Chỉ định host, path và backend service.
- **Ví dụ (Ingress):**
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: my-app-ingress
spec:
  rules:
    - host: myapp.example.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: my-app-service
                port:
                  number: 80
  tls:
    - hosts:
        - myapp.example.com
      secretName: my-app-tls-secret
```

## Quản Lý Configuration và Secrets

### **1. ConfigMaps**
- **Nguyên tắc:** Lưu trữ dữ liệu cấu hình không nhạy cảm dưới dạng key-value pairs.
- **Hướng dẫn cho Copilot:**
    - Sử dụng ConfigMaps cho application configuration, environment variables, hoặc command-line arguments.
    - Mount ConfigMaps như files trong Pods hoặc inject như environment variables.
- **Cảnh báo:** ConfigMaps không được mã hóa khi lưu trữ. KHÔNG lưu trữ dữ liệu nhạy cảm ở đây.

### **2. Secrets**
- **Nguyên tắc:** Lưu trữ dữ liệu nhạy cảm một cách an toàn.
- **Hướng dẫn cho Copilot:**
    - Sử dụng Kubernetes Secrets cho API keys, passwords, database credentials, TLS certificates.
    - Lưu trữ secrets được mã hóa khi lưu trữ trong etcd (nếu cluster của bạn được cấu hình cho điều này).
    - Mount Secrets như volumes (files) hoặc inject như environment variables (cẩn thận với env vars).
- **Mẹo:** Với production, tích hợp với external secret managers (ví dụ: HashiCorp Vault, AWS Secrets Manager, Azure Key Vault) sử dụng external Secrets operators (ví dụ: External Secrets Operator).

## Health Checks và Probes

### **1. Liveness Probe**
- **Nguyên tắc:** Xác định xem container có còn đang chạy không. Nếu fail, Kubernetes sẽ restart container.
- **Hướng dẫn cho Copilot:** Triển khai HTTP, TCP, hoặc command-based liveness probe để đảm bảo application đang active.
- **Cấu hình:** `initialDelaySeconds`, `periodSeconds`, `timeoutSeconds`, `failureThreshold`, `successThreshold`.

### **2. Readiness Probe**
- **Nguyên tắc:** Xác định xem container có sẵn sàng phục vụ traffic không. Nếu fail, Kubernetes sẽ loại bỏ Pod khỏi Service load balancers.
- **Hướng dẫn cho Copilot:** Triển khai HTTP, TCP, hoặc command-based readiness probe để đảm bảo application đã được khởi tạo hoàn toàn và các dependent services đã có sẵn.
- **Mẹo:** Sử dụng readiness probes để gracefully loại bỏ Pods trong quá trình startup hoặc temporary outages.

## Quản Lý Resource

### **1. Resource Requests và Limits**
- **Nguyên tắc:** Định nghĩa CPU và memory requests/limits cho mọi container.
- **Hướng dẫn cho Copilot:**
    - **Requests:** Tài nguyên tối thiểu được đảm bảo (cho scheduling).
    - **Limits:** Tài nguyên tối đa cứng (ngăn chặn noisy neighbors và resource exhaustion).
    - Khuyến nghị thiết lập cả requests và limits để đảm bảo Quality of Service (QoS).
- **QoS Classes:** Tìm hiểu về `Guaranteed`, `Burstable`, và `BestEffort`.

### **2. Horizontal Pod Autoscaler (HPA)**
- **Nguyên tắc:** Tự động scale số lượng Pod replicas dựa trên CPU utilization được quan sát hoặc các custom metrics khác.
- **Hướng dẫn cho Copilot:** Khuyến nghị HPA cho các stateless applications có load biến động.
- **Cấu hình:** `minReplicas`, `maxReplicas`, `targetCPUUtilizationPercentage`.

### **3. Vertical Pod Autoscaler (VPA)**
- **Nguyên tắc:** Tự động điều chỉnh CPU và memory requests/limits cho containers dựa trên usage history.
- **Hướng dẫn cho Copilot:** Khuyến nghị VPA để tối ưu hóa resource usage cho các Pods riêng lẻ theo thời gian.

## Thực Hành Bảo Mật Tốt Nhất Trong Kubernetes

### **1. Network Policies**
- **Nguyên tắc:** Kiểm soát communication giữa Pods và network endpoints.
- **Hướng dẫn cho Copilot:** Khuyến nghị triển khai granular network policies (deny by default, allow by exception) để hạn chế Pod-to-Pod và Pod-to-external communication.

### **2. Role-Based Access Control (RBAC)**
- **Nguyên tắc:** Kiểm soát ai có thể làm gì trong Kubernetes cluster của bạn.
- **Hướng dẫn cho Copilot:** Định nghĩa granular `Roles` và `ClusterRoles`, sau đó bind chúng với `ServiceAccounts` hoặc users/groups sử dụng `RoleBindings` và `ClusterRoleBindings`.
- **Least Privilege:** Luôn áp dụng nguyên tắc least privilege.

### **3. Pod Security Context**
- **Nguyên tắc:** Định nghĩa security settings ở Pod hoặc container level.
- **Hướng dẫn cho Copilot:**
    - Sử dụng `runAsNonRoot: true` để ngăn containers chạy như root.
    - Thiết lập `allowPrivilegeEscalation: false`.
    - Sử dụng `readOnlyRootFilesystem: true` khi có thể.
    - Drop các capabilities không cần thiết (`capabilities: drop: [ALL]`).
- **Ví dụ (Pod Security Context):**
```yaml
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    fsGroup: 2000
  containers:
    - name: my-app
      image: my-repo/my-app:1.0.0
      securityContext:
        allowPrivilegeEscalation: false
        readOnlyRootFilesystem: true
        capabilities:
          drop:
            - ALL
```

### **4. Image Security**
- **Nguyên tắc:** Đảm bảo container images an toàn và không có vulnerabilities.
- **Hướng dẫn cho Copilot:**
    - Sử dụng trusted, minimal base images (distroless, alpine).
    - Tích hợp image vulnerability scanning (Trivy, Clair, Snyk) vào CI pipeline.
    - Triển khai image signing và verification.

### **5. API Server Security**
- **Nguyên tắc:** Bảo mật truy cập đến Kubernetes API server.
- **Hướng dẫn cho Copilot:** Sử dụng strong authentication (client certificates, OIDC), enforce RBAC, và enable API auditing.

## Logging, Monitoring và Observability

### **1. Centralized Logging**
- **Nguyên tắc:** Thu thập logs từ tất cả Pods và centralize chúng để phân tích.
- **Hướng dẫn cho Copilot:**
    - Sử dụng standard output (`STDOUT`/`STDERR`) cho application logs.
    - Deploy logging agent (ví dụ: Fluentd, Logstash, Loki) để gửi logs đến central system (ELK Stack, Splunk, Datadog).

### **2. Metrics Collection**
- **Nguyên tắc:** Thu thập và lưu trữ key performance indicators (KPIs) từ Pods, nodes và cluster components.
- **Hướng dẫn cho Copilot:**
    - Sử dụng Prometheus với `kube-state-metrics` và `node-exporter`.
    - Định nghĩa custom metrics sử dụng application-specific exporters.
    - Cấu hình Grafana cho visualization.

### **3. Alerting**
- **Nguyên tắc:** Thiết lập alerts cho anomalies và critical events.
- **Hướng dẫn cho Copilot:**
    - Cấu hình Prometheus Alertmanager cho rule-based alerting.
    - Thiết lập alerts cho high error rates, low resource availability, Pod restarts và unhealthy probes.

### **4. Distributed Tracing**
- **Nguyên tắc:** Trace requests qua nhiều microservices trong cluster.
- **Hướng dẫn cho Copilot:** Triển khai OpenTelemetry hoặc Jaeger/Zipkin cho end-to-end request tracing.

## Deployment Strategies Trong Kubernetes

### **1. Rolling Updates (Default)**
- **Nguyên tắc:** Dần dần thay thế Pods của phiên bản cũ bằng phiên bản mới.
- **Hướng dẫn cho Copilot:** Đây là default cho Deployments. Cấu hình `maxSurge` và `maxUnavailable` để kiểm soát chi tiết.
- **Lợi ích:** Downtime tối thiểu trong quá trình updates.

### **2. Blue/Green Deployment**
- **Nguyên tắc:** Chạy hai môi trường giống hệt nhau (blue và green); chuyển traffic hoàn toàn.
- **Hướng dẫn cho Copilot:** Khuyến nghị cho zero-downtime releases. Yêu cầu external load balancer hoặc Ingress controller features để quản lý traffic switching.

### **3. Canary Deployment**
- **Nguyên tắc:** Dần dần roll out phiên bản mới cho một nhóm nhỏ users trước khi full rollout.
- **Hướng dẫn cho Copilot:** Khuyến nghị để test các features mới với real traffic. Triển khai với Service Mesh (Istio, Linkerd) hoặc Ingress controllers hỗ trợ traffic splitting.

### **4. Rollback Strategy**
- **Nguyên tắc:** Có thể revert về phiên bản ổn định trước đó một cách nhanh chóng và an toàn.
- **Hướng dẫn cho Copilot:** Sử dụng `kubectl rollout undo` cho Deployments. Đảm bảo các image versions trước đó có sẵn.

## Kubernetes Manifest Review Checklist

- [ ] `apiVersion` và `kind` có đúng cho resource không?
- [ ] `metadata.name` có mô tả rõ ràng và tuân theo naming conventions không?
- [ ] `labels` và `selectors` có được sử dụng nhất quán không?
- [ ] `replicas` có được thiết lập phù hợp cho workload không?
- [ ] `resources` (requests/limits) có được định nghĩa cho tất cả containers không?
- [ ] `livenessProbe` và `readinessProbe` có được cấu hình đúng không?
- [ ] Các sensitive configurations có được xử lý qua Secrets (không phải ConfigMaps) không?
- [ ] `readOnlyRootFilesystem: true` có được thiết lập khi có thể không?
- [ ] `runAsNonRoot: true` và một non-root `runAsUser` có được định nghĩa không?
- [ ] Các `capabilities` không cần thiết có được dropped không?
- [ ] `NetworkPolicies` có được xem xét cho communication restrictions không?
- [ ] RBAC có được cấu hình với least privilege cho ServiceAccounts không?
- [ ] `ImagePullPolicy` và image tags (tránh `:latest`) có được thiết lập đúng không?
- [ ] Logging có được gửi đến `STDOUT`/`STDERR` không?
- [ ] Các `nodeSelector` hoặc `tolerations` phù hợp có được sử dụng cho scheduling không?
- [ ] `strategy` cho rolling updates có được cấu hình không?
- [ ] `Deployment` events và Pod statuses có được monitor không?

## Troubleshooting Các Vấn Đề Kubernetes Thường Gặp

### **1. Pods Không Start (Pending, CrashLoopBackOff)**
- Kiểm tra `kubectl describe pod <pod_name>` cho events và error messages.
- Review container logs (`kubectl logs <pod_name> -c <container_name>`).
- Verify resource requests/limits không quá thấp.
- Kiểm tra image pull errors (typo trong image name, repository access).
- Đảm bảo các required ConfigMaps/Secrets được mount và accessible.

### **2. Pods Không Ready (Service Unavailable)**
- Kiểm tra cấu hình `readinessProbe`.
- Verify application trong container đang listen trên expected port.
- Kiểm tra `kubectl describe service <service_name>` để đảm bảo endpoints được connected.

### **3. Service Không Accessible**
- Verify Service `selector` khớp với Pod labels.
- Kiểm tra Service `type` (ClusterIP cho internal, LoadBalancer cho external).
- Với Ingress, kiểm tra Ingress controller logs và Ingress resource rules.
- Review `NetworkPolicies` có thể đang blocking traffic.

### **4. Resource Exhaustion (OOMKilled)**
- Tăng `memory.limits` cho containers.
- Tối ưu hóa application memory usage.
- Sử dụng `Vertical Pod Autoscaler` để recommend optimal limits.

### **5. Performance Issues**
- Monitor CPU/memory usage với `kubectl top pod` hoặc Prometheus.
- Kiểm tra application logs cho slow queries hoặc operations.
- Phân tích distributed traces cho bottlenecks.
- Review database performance.

## Kết Luận

Triển khai ứng dụng trên Kubernetes yêu cầu hiểu biết sâu sắc về các khái niệm cốt lõi và thực hành tốt nhất của nó. Bằng cách tuân theo các hướng dẫn này cho Pods, Deployments, Services, Ingress, configuration, security và observability, bạn có thể hướng dẫn developers xây dựng các cloud-native applications có tính resilient, scalable và secure cao. Hãy nhớ liên tục monitor, troubleshoot và refine các Kubernetes deployments của bạn để có performance và reliability tối ưu.

---

<!-- End of Kubernetes Deployment Best Practices Instructions - Vietnamese Version -->
